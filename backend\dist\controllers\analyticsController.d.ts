import { Request, Response } from 'express';
/**
 * Get cash flow analytics (basic for all plans, advanced for paid plans)
 */
export declare const getCashFlowAnalytics: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
/**
 * Get dashboard analytics summary
 */
export declare const getDashboardAnalytics: (req: Request, res: Response) => Promise<void>;
/**
 * Get revenue analytics
 */
export declare const getRevenueAnalytics: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
/**
 * Get customer analytics
 */
export declare const getCustomerAnalytics: (req: Request, res: Response) => Promise<void>;
//# sourceMappingURL=analyticsController.d.ts.map