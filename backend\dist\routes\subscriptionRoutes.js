"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const subscriptionController_1 = require("../controllers/subscriptionController");
const auth_1 = require("../middleware/auth");
const router = express_1.default.Router();
// Public routes
router.get('/plans', subscriptionController_1.getPlans);
// Protected routes
router.use(auth_1.authenticate);
// Subscription management
router.get('/current', subscriptionController_1.getCurrentSubscription);
router.post('/change', subscriptionController_1.changeSubscription);
router.post('/cancel', subscriptionController_1.cancelSubscription);
router.post('/trial', subscriptionController_1.startFreeTrial);
// Payment routes
router.post('/payment/create-order', subscriptionController_1.createPaymentOrder);
router.post('/payment/verify', subscriptionController_1.verifyPayment);
router.post('/payment/failure', subscriptionController_1.handlePaymentFailure);
router.get('/payment/history', subscriptionController_1.getPaymentHistory);
// Usage tracking
router.get('/usage/check', subscriptionController_1.checkUsageLimit);
router.post('/usage/sync', subscriptionController_1.syncUsage);
// Admin routes (add admin middleware if needed)
router.get('/analytics/revenue', subscriptionController_1.getRevenueAnalytics);
exports.default = router;
//# sourceMappingURL=subscriptionRoutes.js.map