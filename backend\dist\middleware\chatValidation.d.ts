import { Request, Response, NextFunction } from 'express';
export declare const validateChatMessage: (req: Request, res: Response, next: NextFunction) => void;
export declare const validateChatSession: (req: Request, res: Response, next: NextFunction) => void;
export declare const validateChatSessionUpdate: (req: Request, res: Response, next: NextFunction) => void;
export declare const rateLimitChatMessages: (req: Request, res: Response, next: NextFunction) => void;
//# sourceMappingURL=chatValidation.d.ts.map