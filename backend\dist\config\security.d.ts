export interface SecurityConfig {
    jwt: {
        secret: string;
        expiresIn: string;
        refreshExpiresIn: string;
        algorithm: string;
    };
    encryption: {
        algorithm: string;
        keyLength: number;
        ivLength: number;
    };
    password: {
        saltRounds: number;
        minLength: number;
        requireUppercase: boolean;
        requireLowercase: boolean;
        requireNumbers: boolean;
        requireSpecialChars: boolean;
    };
    rateLimit: {
        windowMs: number;
        maxRequests: number;
        authMaxRequests: number;
        otpMaxRequests: number;
    };
    session: {
        maxAge: number;
        secure: boolean;
        httpOnly: boolean;
        sameSite: 'strict' | 'lax' | 'none';
    };
    cors: {
        origin: string | string[];
        credentials: boolean;
        methods: string[];
        allowedHeaders: string[];
    };
    upload: {
        maxFileSize: number;
        allowedMimeTypes: string[];
        maxFiles: number;
    };
    api: {
        maxRequestSize: string;
        timeout: number;
    };
}
export declare const getSecurityConfig: () => SecurityConfig;
export declare class EncryptionService {
    private static readonly algorithm;
    private static readonly keyLength;
    private static readonly ivLength;
    private static readonly tagLength;
    /**
     * Generate a secure encryption key
     */
    static generateKey(): string;
    /**
     * Encrypt sensitive data
     */
    static encrypt(text: string, key?: string): {
        encrypted: string;
        iv: string;
        tag: string;
    };
    /**
     * Decrypt sensitive data
     */
    static decrypt(encryptedData: {
        encrypted: string;
        iv: string;
        tag: string;
    }, key: string): string;
    /**
     * Hash sensitive data (one-way)
     */
    static hash(data: string): string;
    /**
     * Generate secure random token
     */
    static generateToken(length?: number): string;
    /**
     * Generate secure API key
     */
    static generateApiKey(): string;
}
export declare class PasswordValidator {
    private static config;
    /**
     * Validate password strength
     */
    static validate(password: string): {
        isValid: boolean;
        errors: string[];
    };
    /**
     * Generate secure password
     */
    static generate(length?: number): string;
}
export declare const SECURITY_CONSTANTS: {
    readonly MAX_LOGIN_ATTEMPTS: 5;
    readonly LOCKOUT_DURATION: number;
    readonly OTP_EXPIRY: number;
    readonly SESSION_TIMEOUT: number;
    readonly API_KEY_EXPIRY: number;
    readonly REFRESH_TOKEN_EXPIRY: number;
    readonly PASSWORD_RESET_EXPIRY: number;
    readonly EMAIL_VERIFICATION_EXPIRY: number;
};
export default getSecurityConfig;
//# sourceMappingURL=security.d.ts.map