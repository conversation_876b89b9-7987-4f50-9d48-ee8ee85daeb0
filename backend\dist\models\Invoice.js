"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importStar(require("mongoose"));
const crypto_1 = __importDefault(require("crypto"));
const CustomerSchema = new mongoose_1.Schema({
    name: { type: String, required: true, trim: true },
    email: { type: String, trim: true, lowercase: true },
    phone: { type: String, trim: true },
    gstNumber: {
        type: String,
        trim: true,
        uppercase: true,
        match: [/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/, 'Invalid GST number format'],
        required: false // Made optional for non-GST customers
    },
    address: {
        street: { type: String, required: true, trim: true },
        city: { type: String, required: true, trim: true },
        state: { type: String, required: true, trim: true },
        pincode: { type: String, required: true, trim: true },
        country: { type: String, default: 'India', trim: true }
    }
});
const InvoiceItemSchema = new mongoose_1.Schema({
    description: { type: String, required: true, trim: true },
    hsn: { type: String, trim: true, default: '' }, // HSN/SAC code - optional for non-GST
    quantity: { type: Number, required: true, min: 0 },
    unit: { type: String, required: true, trim: true },
    rate: { type: Number, required: true, min: 0 },
    discount: { type: Number, default: 0, min: 0 },
    taxableAmount: { type: Number, required: true, min: 0 },
    cgstRate: { type: Number, default: 0, min: 0, max: 50 },
    sgstRate: { type: Number, default: 0, min: 0, max: 50 },
    igstRate: { type: Number, default: 0, min: 0, max: 50 },
    cgstAmount: { type: Number, default: 0, min: 0 },
    sgstAmount: { type: Number, default: 0, min: 0 },
    igstAmount: { type: Number, default: 0, min: 0 },
    totalAmount: { type: Number, required: true, min: 0 }
});
const InvoiceSchema = new mongoose_1.Schema({
    invoiceNumber: {
        type: String,
        required: true,
        unique: true,
        trim: true
    },
    invoiceDate: {
        type: Date,
        required: true,
        default: Date.now
    },
    dueDate: {
        type: Date
    },
    userId: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    invoiceType: {
        type: String,
        enum: ['gst', 'non-gst'],
        required: true,
        default: 'gst'
    },
    customer: {
        type: CustomerSchema,
        required: true
    },
    items: {
        type: [InvoiceItemSchema],
        required: true,
        validate: {
            validator: function (items) {
                return items.length > 0;
            },
            message: 'Invoice must have at least one item'
        }
    },
    subtotal: { type: Number, required: true, min: 0 },
    totalDiscount: { type: Number, default: 0, min: 0 },
    taxableAmount: { type: Number, required: true, min: 0 },
    totalCGST: { type: Number, required: true, min: 0 },
    totalSGST: { type: Number, required: true, min: 0 },
    totalIGST: { type: Number, required: true, min: 0 },
    totalTax: { type: Number, required: true, min: 0 },
    grandTotal: { type: Number, required: true, min: 0 },
    notes: { type: String, trim: true },
    terms: { type: String, trim: true },
    status: {
        type: String,
        enum: ['draft', 'sent', 'paid', 'overdue', 'cancelled'],
        default: 'draft'
    },
    paymentStatus: {
        type: String,
        enum: ['pending', 'partial', 'paid'],
        default: 'pending'
    },
    paymentDate: { type: Date },
    hash: { type: String, default: '' },
    remindersSent: [{
            type: {
                type: String,
                enum: ['upcoming', 'due', 'overdue'],
                required: true
            },
            sentAt: {
                type: Date,
                required: true,
                default: Date.now
            },
            days: {
                type: Number,
                required: true
            }
        }]
}, {
    timestamps: true
});
// Indexes for better query performance
InvoiceSchema.index({ userId: 1, invoiceNumber: 1 });
InvoiceSchema.index({ userId: 1, status: 1 });
InvoiceSchema.index({ userId: 1, paymentStatus: 1 });
InvoiceSchema.index({ invoiceDate: -1 });
InvoiceSchema.index({ dueDate: 1 });
// Generate invoice number
InvoiceSchema.statics.generateInvoiceNumber = async function (userId) {
    const year = new Date().getFullYear();
    const month = String(new Date().getMonth() + 1).padStart(2, '0');
    // Find the last invoice for this user in current month
    const lastInvoice = await this.findOne({
        userId,
        invoiceNumber: new RegExp(`^INV-${year}${month}-`)
    }).sort({ invoiceNumber: -1 });
    let sequence = 1;
    if (lastInvoice) {
        const lastSequence = parseInt(lastInvoice.invoiceNumber.split('-')[2]);
        sequence = lastSequence + 1;
    }
    return `INV-${year}${month}-${String(sequence).padStart(4, '0')}`;
};
// Generate hash for blockchain integrity
InvoiceSchema.methods.generateHash = function () {
    try {
        const data = {
            invoiceNumber: this.invoiceNumber || '',
            invoiceDate: this.invoiceDate || new Date(),
            userId: this.userId || '',
            customer: this.customer || {},
            items: this.items || [],
            grandTotal: this.grandTotal || 0
        };
        const hash = crypto_1.default.createHash('sha256')
            .update(JSON.stringify(data))
            .digest('hex');
        console.log('Generated hash for invoice:', this.invoiceNumber, 'Hash:', hash);
        return hash;
    }
    catch (error) {
        console.error('Error in generateHash:', error);
        // Return a fallback hash if generation fails
        return crypto_1.default.createHash('sha256')
            .update(`${this.invoiceNumber || 'unknown'}-${Date.now()}`)
            .digest('hex');
    }
};
// Pre-save middleware to generate hash
InvoiceSchema.pre('save', function (next) {
    try {
        // Always generate hash for new invoices or when modified
        if (this.isModified() || this.isNew || !this.hash) {
            const generatedHash = this.generateHash();
            if (generatedHash) {
                this.hash = generatedHash;
                console.log('Hash set for invoice:', this.invoiceNumber, 'Hash:', this.hash);
            }
            else {
                console.error('Failed to generate hash for invoice:', this.invoiceNumber);
                return next(new Error('Failed to generate invoice hash'));
            }
        }
        next();
    }
    catch (error) {
        console.error('Error in pre-save middleware:', error);
        next(error);
    }
});
// Method to verify invoice integrity
InvoiceSchema.methods.verifyIntegrity = function () {
    const currentHash = this.generateHash();
    return currentHash === this.hash;
};
exports.default = mongoose_1.default.model('Invoice', InvoiceSchema);
//# sourceMappingURL=Invoice.js.map