"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.Notification = exports.NotificationPreference = void 0;
const mongoose_1 = __importStar(require("mongoose"));
const NotificationPreferenceSchema = new mongoose_1.Schema({
    userId: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User',
        required: true,
        unique: true
    },
    emailNotifications: {
        type: Boolean,
        default: true
    },
    emailAddress: {
        type: String,
        trim: true,
        lowercase: true,
        match: [
            /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
            'Please enter a valid email'
        ]
    },
    complianceReminders: {
        type: Boolean,
        default: true
    },
    invoiceReminders: {
        type: Boolean,
        default: true
    },
    systemUpdates: {
        type: Boolean,
        default: true
    },
    marketingEmails: {
        type: Boolean,
        default: false
    },
    reminderTiming: {
        days: [{
                type: Number,
                min: 0,
                max: 365
            }],
        timeOfDay: {
            type: String,
            default: '09:00',
            match: [/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format']
        },
        timezone: {
            type: String,
            default: 'Asia/Kolkata'
        }
    },
    maxDailyEmails: {
        type: Number,
        default: 5,
        min: 1,
        max: 20
    },
    digestMode: {
        type: Boolean,
        default: false
    }
}, {
    timestamps: true
});
const NotificationSchema = new mongoose_1.Schema({
    userId: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    title: {
        type: String,
        required: [true, 'Title is required'],
        trim: true,
        maxlength: [200, 'Title cannot exceed 200 characters']
    },
    message: {
        type: String,
        required: [true, 'Message is required'],
        trim: true,
        maxlength: [1000, 'Message cannot exceed 1000 characters']
    },
    type: {
        type: String,
        enum: ['compliance', 'invoice', 'system', 'reminder', 'alert'],
        required: true
    },
    priority: {
        type: String,
        enum: ['low', 'medium', 'high', 'urgent'],
        default: 'medium'
    },
    relatedEntity: {
        type: {
            type: String,
            enum: ['compliance', 'invoice', 'user']
        },
        id: {
            type: mongoose_1.Schema.Types.ObjectId
        }
    },
    channels: {
        email: {
            type: Boolean,
            default: false
        },
        inApp: {
            type: Boolean,
            default: true
        },
        push: {
            type: Boolean,
            default: false
        }
    },
    status: {
        type: String,
        enum: ['pending', 'sent', 'delivered', 'failed', 'read'],
        default: 'pending'
    },
    sentAt: {
        type: Date
    },
    deliveredAt: {
        type: Date
    },
    readAt: {
        type: Date
    },
    emailDetails: {
        subject: { type: String, trim: true },
        htmlContent: { type: String },
        textContent: { type: String },
        attachments: [{ type: String }]
    },
    retryCount: {
        type: Number,
        default: 0
    },
    maxRetries: {
        type: Number,
        default: 3
    },
    nextRetryAt: {
        type: Date
    },
    scheduledFor: {
        type: Date
    },
    errorMessage: {
        type: String,
        trim: true
    }
}, {
    timestamps: true
});
// Indexes for better performance
NotificationSchema.index({ userId: 1, status: 1 });
NotificationSchema.index({ scheduledFor: 1, status: 1 });
NotificationSchema.index({ type: 1, createdAt: -1 });
NotificationSchema.index({ userId: 1, createdAt: -1 });
exports.NotificationPreference = mongoose_1.default.model('NotificationPreference', NotificationPreferenceSchema);
exports.Notification = mongoose_1.default.model('Notification', NotificationSchema);
//# sourceMappingURL=Notification.js.map