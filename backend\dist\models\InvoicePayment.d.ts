import mongoose, { Document } from 'mongoose';
export interface IInvoicePayment extends Document {
    invoiceId: mongoose.Types.ObjectId;
    userId: mongoose.Types.ObjectId;
    paymentId: string;
    transactionId?: string;
    amount: number;
    currency: string;
    paymentMethod: 'upi' | 'bank_transfer' | 'cash' | 'cheque' | 'card' | 'other';
    paymentGateway?: 'razorpay' | 'payu' | 'cashfree' | 'manual';
    upiTransactionId?: string;
    upiId?: string;
    bankTransactionId?: string;
    bankReference?: string;
    status: 'pending' | 'completed' | 'failed' | 'refunded' | 'cancelled';
    paymentDate: Date;
    gatewayResponse?: any;
    failureReason?: string;
    isVerified: boolean;
    verifiedAt?: Date;
    verifiedBy?: mongoose.Types.ObjectId;
    receiptNumber: string;
    receiptGenerated: boolean;
    receiptUrl?: string;
    customerName?: string;
    customerEmail?: string;
    customerPhone?: string;
    notes?: string;
    metadata?: Record<string, any>;
    createdAt: Date;
    updatedAt: Date;
}
declare const _default: mongoose.Model<IInvoicePayment, {}, {}, {}, mongoose.Document<unknown, {}, IInvoicePayment, {}> & IInvoicePayment & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
export default _default;
//# sourceMappingURL=InvoicePayment.d.ts.map