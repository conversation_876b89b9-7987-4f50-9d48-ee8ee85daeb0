import { IPaymentToken } from '../models/PaymentToken';
export interface SecurePaymentLinkData {
    invoiceId: string;
    amount: number;
    expiryHours?: number;
}
export interface PaymentInitiationData {
    tokenId: string;
    customerInfo?: {
        name?: string;
        email?: string;
        phone?: string;
    };
    paymentMethod: 'upi' | 'bank_transfer' | 'card' | 'other';
    ip?: string;
}
declare class SecurePaymentService {
    /**
     * Generate a secure one-time payment token and QR code
     */
    generateSecurePaymentLink(data: SecurePaymentLinkData): Promise<{
        token: IPaymentToken;
        qrCodeDataUrl: string;
        paymentUrl: string;
    }>;
    /**
     * Validate payment token and get payment details
     */
    validatePaymentToken(tokenId: string, ip?: string): Promise<{
        isValid: boolean;
        token?: IPaymentToken;
        invoice?: any;
        seller?: any;
        error?: string;
    }>;
    /**
     * Initiate payment using secure token
     */
    initiatePayment(data: PaymentInitiationData): Promise<{
        success: boolean;
        paymentId?: string;
        error?: string;
    }>;
    /**
     * Complete payment using secure token
     */
    completePayment(tokenId: string, transactionId: string, paymentData: {
        gatewayResponse?: any;
        customerInfo?: {
            name?: string;
            email?: string;
            phone?: string;
        };
    }): Promise<{
        success: boolean;
        paymentId?: string;
        receiptNumber?: string;
        error?: string;
    }>;
    /**
     * Get payment token details
     */
    getPaymentTokenDetails(tokenId: string): Promise<IPaymentToken | null>;
    /**
     * Get all payment tokens for an invoice
     */
    getInvoicePaymentTokens(invoiceId: string): Promise<IPaymentToken[]>;
    /**
     * Cleanup expired tokens (run periodically)
     */
    cleanupExpiredTokens(): Promise<number>;
    /**
     * Generate UPI payment URL with secure token
     */
    generateSecureUPILink(invoiceId: string, upiId: string, businessName: string, amount: number): Promise<{
        upiUrl: string;
        qrCodeDataUrl: string;
        tokenId: string;
    }>;
}
declare const _default: SecurePaymentService;
export default _default;
//# sourceMappingURL=securePaymentService.d.ts.map