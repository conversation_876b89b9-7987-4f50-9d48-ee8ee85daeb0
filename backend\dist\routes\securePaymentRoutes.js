"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const securePaymentController_1 = require("../controllers/securePaymentController");
const auth_1 = require("../middleware/auth");
const router = express_1.default.Router();
// Generate secure payment link (authenticated)
router.post('/generate-link', auth_1.authenticate, securePaymentController_1.generatePaymentLink);
// Validate payment token (public - no auth required)
router.get('/validate/:tokenId', securePaymentController_1.validatePaymentToken);
// Initiate payment using secure token (public - no auth required)
router.post('/initiate/:tokenId', securePaymentController_1.initiateSecurePayment);
// Complete payment using secure token (public - no auth required)
router.post('/complete/:tokenId', securePaymentController_1.completeSecurePayment);
// Get payment token details (authenticated)
router.get('/token/:tokenId', auth_1.authenticate, securePaymentController_1.getPaymentTokenDetails);
// Get invoice payment tokens (authenticated)
router.get('/invoice/:invoiceId/tokens', auth_1.authenticate, securePaymentController_1.getInvoicePaymentTokens);
// UPI callback handler (webhook - no auth required)
router.post('/upi-callback/:tokenId', securePaymentController_1.upiCallback);
// Cleanup expired tokens (authenticated - admin only)
router.post('/cleanup-expired', auth_1.authenticate, securePaymentController_1.cleanupExpiredTokens);
exports.default = router;
//# sourceMappingURL=securePaymentRoutes.js.map