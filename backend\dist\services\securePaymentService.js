"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const PaymentToken_1 = __importDefault(require("../models/PaymentToken"));
const Invoice_1 = __importDefault(require("../models/Invoice"));
const User_1 = __importDefault(require("../models/User"));
const invoicePaymentService_1 = __importDefault(require("./invoicePaymentService"));
const qrcode_1 = __importDefault(require("qrcode"));
class SecurePaymentService {
    /**
     * Generate a secure one-time payment token and QR code
     */
    async generateSecurePaymentLink(data) {
        try {
            // Validate invoice
            const invoice = await Invoice_1.default.findById(data.invoiceId);
            if (!invoice) {
                throw new Error('Invoice not found');
            }
            if (invoice.paymentStatus === 'paid') {
                throw new Error('Invoice is already paid');
            }
            // Get seller info
            const seller = await User_1.default.findById(invoice.userId);
            if (!seller) {
                throw new Error('Seller not found');
            }
            // Generate secure payment token
            const token = await PaymentToken_1.default.generatePaymentToken(data.invoiceId, invoice.userId.toString(), data.amount, data.expiryHours || 24);
            // Create secure payment URL
            const baseUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
            const paymentUrl = `${baseUrl}/pay/${token.tokenId}`;
            // Generate QR code with secure payment URL
            const qrCodeDataUrl = await qrcode_1.default.toDataURL(paymentUrl, {
                width: 120,
                margin: 1,
                color: {
                    dark: '#1a202c',
                    light: '#ffffff'
                }
            });
            console.log(`🔐 Secure payment token generated: ${token.tokenId} for invoice ${invoice.invoiceNumber}`);
            return {
                token,
                qrCodeDataUrl,
                paymentUrl
            };
        }
        catch (error) {
            console.error('Error generating secure payment link:', error);
            throw error;
        }
    }
    /**
     * Validate payment token and get payment details
     */
    async validatePaymentToken(tokenId, ip) {
        try {
            const token = await PaymentToken_1.default.findOne({ tokenId });
            if (!token) {
                return { isValid: false, error: 'Payment token not found' };
            }
            // Track the scan
            await token.trackScan(ip);
            if (!token.isValid()) {
                if (token.isUsed) {
                    return { isValid: false, error: 'Payment has already been completed' };
                }
                else {
                    return { isValid: false, error: 'Payment link has expired' };
                }
            }
            // Get invoice and seller details
            const [invoice, seller] = await Promise.all([
                Invoice_1.default.findById(token.invoiceId),
                User_1.default.findById(token.userId)
            ]);
            if (!invoice || !seller) {
                return { isValid: false, error: 'Invoice or seller not found' };
            }
            return {
                isValid: true,
                token,
                invoice,
                seller
            };
        }
        catch (error) {
            console.error('Error validating payment token:', error);
            return { isValid: false, error: 'Error validating payment token' };
        }
    }
    /**
     * Initiate payment using secure token
     */
    async initiatePayment(data) {
        try {
            // Validate token
            const validation = await this.validatePaymentToken(data.tokenId, data.ip);
            if (!validation.isValid || !validation.token || !validation.invoice) {
                return { success: false, error: validation.error };
            }
            const { token, invoice } = validation;
            // Record the payment
            const payment = await invoicePaymentService_1.default.recordPayment({
                invoiceId: invoice._id.toString(),
                amount: token.amount,
                paymentMethod: data.paymentMethod,
                customerName: data.customerInfo?.name,
                customerEmail: data.customerInfo?.email,
                customerPhone: data.customerInfo?.phone,
                notes: `Payment via secure token: ${token.tokenId}`
            });
            // Mark token as used
            await token.markAsUsed(payment.paymentId);
            console.log(`💰 Payment initiated via secure token: ${token.tokenId} -> ${payment.paymentId}`);
            return {
                success: true,
                paymentId: payment.paymentId
            };
        }
        catch (error) {
            console.error('Error initiating payment:', error);
            return { success: false, error: 'Error processing payment' };
        }
    }
    /**
     * Complete payment using secure token
     */
    async completePayment(tokenId, transactionId, paymentData) {
        try {
            // Find the token
            const token = await PaymentToken_1.default.findOne({ tokenId });
            if (!token || !token.paymentId) {
                return { success: false, error: 'Invalid payment token or payment not initiated' };
            }
            // Complete the payment
            const payment = await invoicePaymentService_1.default.verifyPayment({
                paymentId: token.paymentId,
                transactionId,
                amount: token.amount,
                status: 'completed',
                gatewayResponse: paymentData.gatewayResponse
            });
            // Update token with transaction ID
            if (!token.transactionId) {
                token.transactionId = transactionId;
                await token.save();
            }
            console.log(`✅ Payment completed via secure token: ${tokenId} -> ${payment.paymentId}`);
            return {
                success: true,
                paymentId: payment.paymentId,
                receiptNumber: payment.receiptNumber
            };
        }
        catch (error) {
            console.error('Error completing payment:', error);
            return { success: false, error: 'Error completing payment' };
        }
    }
    /**
     * Get payment token details
     */
    async getPaymentTokenDetails(tokenId) {
        return await PaymentToken_1.default.findOne({ tokenId });
    }
    /**
     * Get all payment tokens for an invoice
     */
    async getInvoicePaymentTokens(invoiceId) {
        return await PaymentToken_1.default.find({ invoiceId }).sort({ createdAt: -1 });
    }
    /**
     * Cleanup expired tokens (run periodically)
     */
    async cleanupExpiredTokens() {
        const result = await PaymentToken_1.default.deleteMany({
            expiresAt: { $lt: new Date() },
            isUsed: false
        });
        console.log(`🧹 Cleaned up ${result.deletedCount} expired payment tokens`);
        return result.deletedCount;
    }
    /**
     * Generate UPI payment URL with secure token
     */
    async generateSecureUPILink(invoiceId, upiId, businessName, amount) {
        try {
            // Generate secure payment token
            const { token } = await this.generateSecurePaymentLink({
                invoiceId,
                amount,
                expiryHours: 24
            });
            // Create UPI URL with secure callback
            const callbackUrl = `${process.env.BACKEND_URL || 'http://localhost:5000'}/api/payments/upi-callback/${token.tokenId}`;
            const upiUrl = `upi://pay?pa=${encodeURIComponent(upiId)}&pn=${encodeURIComponent(businessName)}&am=${amount}&cu=INR&tn=${encodeURIComponent(`Payment for Invoice - Secure Token: ${token.tokenId}`)}&url=${encodeURIComponent(callbackUrl)}`;
            // Generate QR code
            const qrCodeDataUrl = await qrcode_1.default.toDataURL(upiUrl, {
                width: 120,
                margin: 1,
                color: {
                    dark: '#1a202c',
                    light: '#ffffff'
                }
            });
            return {
                upiUrl,
                qrCodeDataUrl,
                tokenId: token.tokenId
            };
        }
        catch (error) {
            console.error('Error generating secure UPI link:', error);
            throw error;
        }
    }
}
exports.default = new SecurePaymentService();
//# sourceMappingURL=securePaymentService.js.map