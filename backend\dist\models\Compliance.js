"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserCompliance = exports.ComplianceDeadline = void 0;
const mongoose_1 = __importStar(require("mongoose"));
const ComplianceDeadlineSchema = new mongoose_1.Schema({
    title: {
        type: String,
        required: [true, 'Title is required'],
        trim: true,
        maxlength: [200, 'Title cannot exceed 200 characters']
    },
    description: {
        type: String,
        required: [true, 'Description is required'],
        trim: true,
        maxlength: [1000, 'Description cannot exceed 1000 characters']
    },
    type: {
        type: String,
        enum: ['gst', 'tds', 'income_tax', 'pf', 'esi', 'custom'],
        required: true
    },
    category: {
        type: String,
        enum: ['filing', 'payment', 'return', 'audit', 'other'],
        required: true
    },
    dueDate: {
        type: Date,
        required: true
    },
    frequency: {
        type: String,
        enum: ['monthly', 'quarterly', 'annually', 'one_time'],
        required: true
    },
    applicableFor: [{
            type: String,
            trim: true
        }],
    priority: {
        type: String,
        enum: ['low', 'medium', 'high', 'critical'],
        default: 'medium'
    },
    nextDueDate: {
        type: Date
    },
    lastUpdated: {
        type: Date,
        default: Date.now
    },
    penaltyInfo: {
        lateFilingPenalty: { type: String, trim: true },
        interestRate: { type: String, trim: true },
        additionalCharges: { type: String, trim: true }
    },
    resources: {
        officialLink: { type: String, trim: true },
        guideLink: { type: String, trim: true },
        formNumber: { type: String, trim: true }
    },
    isActive: {
        type: Boolean,
        default: true
    }
}, {
    timestamps: true
});
const UserComplianceSchema = new mongoose_1.Schema({
    userId: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    complianceId: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'ComplianceDeadline',
        required: true
    },
    isEnabled: {
        type: Boolean,
        default: true
    },
    customDueDate: {
        type: Date
    },
    reminderDays: [{
            type: Number,
            min: 0,
            max: 365
        }],
    isCompleted: {
        type: Boolean,
        default: false
    },
    completedDate: {
        type: Date
    },
    notes: {
        type: String,
        trim: true,
        maxlength: [500, 'Notes cannot exceed 500 characters']
    },
    nextDueDate: {
        type: Date,
        required: true
    }
}, {
    timestamps: true
});
// Indexes for better performance
ComplianceDeadlineSchema.index({ type: 1, dueDate: 1 });
ComplianceDeadlineSchema.index({ frequency: 1, isActive: 1 });
UserComplianceSchema.index({ userId: 1, nextDueDate: 1 });
UserComplianceSchema.index({ userId: 1, complianceId: 1 }, { unique: true });
exports.ComplianceDeadline = mongoose_1.default.model('ComplianceDeadline', ComplianceDeadlineSchema);
exports.UserCompliance = mongoose_1.default.model('UserCompliance', UserComplianceSchema);
//# sourceMappingURL=Compliance.js.map