import { IInvoicePayment } from '../models/InvoicePayment';
export interface PaymentData {
    invoiceId: string;
    amount: number;
    paymentMethod: 'upi' | 'bank_transfer' | 'cash' | 'cheque' | 'card' | 'other';
    transactionId?: string;
    upiTransactionId?: string;
    upiId?: string;
    bankTransactionId?: string;
    bankReference?: string;
    customerName?: string;
    customerEmail?: string;
    customerPhone?: string;
    notes?: string;
    metadata?: Record<string, any>;
}
export interface PaymentVerificationData {
    paymentId: string;
    transactionId: string;
    amount: number;
    status: 'completed' | 'failed';
    gatewayResponse?: any;
    failureReason?: string;
}
declare class InvoicePaymentService {
    /**
     * Record a new payment
     */
    recordPayment(paymentData: PaymentData): Promise<IInvoicePayment>;
    /**
     * Verify and complete payment
     */
    verifyPayment(verificationData: PaymentVerificationData): Promise<IInvoicePayment>;
    /**
     * Generate payment receipt
     */
    private generateReceipt;
    /**
     * Send payment confirmation emails
     */
    private sendPaymentConfirmation;
    /**
     * Get payment history for an invoice
     */
    getInvoicePayments(invoiceId: string): Promise<IInvoicePayment[]>;
    /**
     * Get payment by ID
     */
    getPaymentById(paymentId: string): Promise<IInvoicePayment | null>;
    /**
     * Get payments for a user
     */
    getUserPayments(userId: string, limit?: number): Promise<IInvoicePayment[]>;
    /**
     * Manual payment verification (for admin)
     */
    manualVerifyPayment(paymentId: string, verifiedBy: string, notes?: string): Promise<IInvoicePayment>;
}
declare const _default: InvoicePaymentService;
export default _default;
//# sourceMappingURL=invoicePaymentService.d.ts.map