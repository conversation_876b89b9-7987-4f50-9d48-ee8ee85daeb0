{"version": 3, "file": "apiAuth.js", "sourceRoot": "", "sources": ["../../src/middleware/apiAuth.ts"], "names": [], "mappings": ";;;;;;AACA,8EAAsD;AAUtD,MAAM,aAAa,GAAG,IAAI,uBAAa,EAAE,CAAC;AAE1C;;GAEG;AACI,MAAM,kBAAkB,GAAG,KAAK,EAAE,GAAmB,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IACjG,IAAI,CAAC;QACH,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC;QAE7C,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YACrD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,4DAA4D;aACtE,CAAC,CAAC;QACL,CAAC;QAED,MAAM,MAAM,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,0BAA0B;QAElE,iBAAiB;QACjB,MAAM,YAAY,GAAG,MAAM,aAAa,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAE9D,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;YACxB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,4BAA4B;aACtC,CAAC,CAAC;QACL,CAAC;QAED,oBAAoB;QACpB,MAAM,SAAS,GAAG,MAAM,aAAa,CAAC,cAAc,CAAC,YAAY,CAAC,KAAM,EAAE,YAAY,CAAC,WAAW,CAAC,CAAC;QAEpG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;YACvB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,8CAA8C;gBACvD,KAAK,EAAE,qBAAqB;aAC7B,CAAC,CAAC;QACL,CAAC;QAED,8BAA8B;QAC9B,GAAG,CAAC,MAAM,GAAG;YACX,MAAM,EAAE,YAAY,CAAC,MAAO;YAC5B,KAAK,EAAE,YAAY,CAAC,KAAM;YAC1B,WAAW,EAAE,YAAY,CAAC,WAAY;SACvC,CAAC;QAEF,yBAAyB;QACzB,IAAI,SAAS,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;YACtC,GAAG,CAAC,SAAS,CAAC,uBAAuB,EAAE,SAAS,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC;QACzE,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;QACzD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,uBAAuB;SACjC,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC;AAtDW,QAAA,kBAAkB,sBAsD7B;AAEF;;GAEG;AACI,MAAM,kBAAkB,GAAG,CAAC,QAAgB,EAAE,MAAc,EAAE,EAAE;IACrE,OAAO,CAAC,GAAmB,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QAChE,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC;YAChB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,6BAA6B;aACvC,CAAC,CAAC;QACL,CAAC;QAED,MAAM,WAAW,GAAG,GAAG,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAErD,IAAI,CAAC,WAAW,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE,CAAC;YACzC,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,uCAAuC,QAAQ,IAAI,MAAM,EAAE;gBACpE,KAAK,EAAE,0BAA0B;aAClC,CAAC,CAAC;QACL,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AArBW,QAAA,kBAAkB,sBAqB7B;AAEF;;GAEG;AACI,MAAM,oBAAoB,GAAG,KAAK,EAAE,GAAQ,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IACxF,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC;IAE7C,IAAI,CAAC,UAAU,EAAE,CAAC;QAChB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,oDAAoD;SAC9D,CAAC,CAAC;IACL,CAAC;IAED,IAAI,UAAU,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;QACxC,yBAAyB;QACzB,OAAO,IAAA,0BAAkB,EAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;IAC5C,CAAC;SAAM,IAAI,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;QAC5C,oDAAoD;QACpD,MAAM,EAAE,YAAY,EAAE,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC3C,OAAO,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;IACtC,CAAC;SAAM,CAAC;QACN,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,+EAA+E;SACzF,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC;AAvBW,QAAA,oBAAoB,wBAuB/B"}