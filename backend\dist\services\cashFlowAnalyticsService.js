"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CashFlowAnalyticsService = void 0;
const Invoice_1 = __importDefault(require("../models/Invoice"));
const date_fns_1 = require("date-fns");
class CashFlowAnalyticsService {
    /**
     * Get comprehensive cash flow analytics for a user
     */
    async getCashFlowAnalytics(userId, period = 'month') {
        try {
            const now = new Date();
            let startDate;
            let endDate;
            // Determine date range based on period
            switch (period) {
                case 'quarter':
                    startDate = (0, date_fns_1.subMonths)((0, date_fns_1.startOfMonth)(now), 3);
                    endDate = (0, date_fns_1.endOfMonth)(now);
                    break;
                case 'year':
                    startDate = (0, date_fns_1.startOfYear)(now);
                    endDate = (0, date_fns_1.endOfYear)(now);
                    break;
                default: // month
                    startDate = (0, date_fns_1.startOfMonth)(now);
                    endDate = (0, date_fns_1.endOfMonth)(now);
            }
            // Get all invoices for the user
            const invoices = await Invoice_1.default.find({
                userId,
                invoiceDate: { $gte: (0, date_fns_1.subMonths)(startDate, 12), $lte: endDate }
            }).sort({ invoiceDate: -1 });
            // Calculate basic metrics
            const totalRevenue = this.calculateTotalRevenue(invoices);
            const totalOutstanding = this.calculateOutstanding(invoices);
            const totalOverdue = this.calculateOverdue(invoices);
            const averagePaymentTime = this.calculateAveragePaymentTime(invoices);
            const cashFlowTrend = this.determineCashFlowTrend(invoices);
            // Generate time-series data
            const monthlyData = this.generateMonthlyData(invoices, 12);
            const weeklyData = this.generateWeeklyData(invoices, 8);
            // Payment status breakdown
            const paymentStatusBreakdown = this.getPaymentStatusBreakdown(invoices);
            // Top customers analysis
            const topCustomers = this.getTopCustomers(invoices);
            // Projected cash flow
            const projectedCashFlow = this.generateCashFlowProjection(invoices);
            return {
                totalRevenue,
                totalOutstanding,
                totalOverdue,
                averagePaymentTime,
                cashFlowTrend,
                monthlyData,
                weeklyData,
                paymentStatusBreakdown,
                topCustomers,
                projectedCashFlow
            };
        }
        catch (error) {
            console.error('Error generating cash flow analytics:', error);
            throw error;
        }
    }
    /**
     * Calculate total revenue from paid invoices
     */
    calculateTotalRevenue(invoices) {
        return invoices
            .filter(invoice => invoice.paymentStatus === 'paid')
            .reduce((total, invoice) => total + invoice.grandTotal, 0);
    }
    /**
     * Calculate total outstanding amount
     */
    calculateOutstanding(invoices) {
        return invoices
            .filter(invoice => invoice.paymentStatus === 'pending')
            .reduce((total, invoice) => total + invoice.grandTotal, 0);
    }
    /**
     * Calculate total overdue amount
     */
    calculateOverdue(invoices) {
        const today = new Date();
        return invoices
            .filter(invoice => invoice.paymentStatus !== 'paid' &&
            invoice.dueDate &&
            new Date(invoice.dueDate) < today)
            .reduce((total, invoice) => total + invoice.grandTotal, 0);
    }
    /**
     * Calculate average payment time in days
     */
    calculateAveragePaymentTime(invoices) {
        const paidInvoices = invoices.filter(invoice => invoice.paymentStatus === 'paid' &&
            invoice.paymentDate &&
            invoice.invoiceDate);
        if (paidInvoices.length === 0)
            return 0;
        const totalDays = paidInvoices.reduce((total, invoice) => {
            const invoiceDate = new Date(invoice.invoiceDate);
            const paymentDate = new Date(invoice.paymentDate);
            const daysDiff = Math.ceil((paymentDate.getTime() - invoiceDate.getTime()) / (1000 * 60 * 60 * 24));
            return total + daysDiff;
        }, 0);
        return Math.round(totalDays / paidInvoices.length);
    }
    /**
     * Determine cash flow trend
     */
    determineCashFlowTrend(invoices) {
        const now = new Date();
        const currentMonth = invoices.filter(invoice => {
            const invoiceDate = new Date(invoice.invoiceDate);
            return invoiceDate.getMonth() === now.getMonth() &&
                invoiceDate.getFullYear() === now.getFullYear() &&
                invoice.paymentStatus === 'paid';
        });
        const lastMonth = invoices.filter(invoice => {
            const invoiceDate = new Date(invoice.invoiceDate);
            const lastMonthDate = (0, date_fns_1.subMonths)(now, 1);
            return invoiceDate.getMonth() === lastMonthDate.getMonth() &&
                invoiceDate.getFullYear() === lastMonthDate.getFullYear() &&
                invoice.paymentStatus === 'paid';
        });
        const currentRevenue = currentMonth.reduce((total, inv) => total + inv.grandTotal, 0);
        const lastRevenue = lastMonth.reduce((total, inv) => total + inv.grandTotal, 0);
        if (currentRevenue > lastRevenue * 1.1)
            return 'positive';
        if (currentRevenue < lastRevenue * 0.9)
            return 'negative';
        return 'stable';
    }
    /**
     * Generate monthly cash flow data
     */
    generateMonthlyData(invoices, months) {
        const data = [];
        const now = new Date();
        for (let i = months - 1; i >= 0; i--) {
            const monthDate = (0, date_fns_1.subMonths)(now, i);
            const monthStart = (0, date_fns_1.startOfMonth)(monthDate);
            const monthEnd = (0, date_fns_1.endOfMonth)(monthDate);
            const monthInvoices = invoices.filter(invoice => {
                const invoiceDate = new Date(invoice.invoiceDate);
                return invoiceDate >= monthStart && invoiceDate <= monthEnd;
            });
            const income = monthInvoices
                .filter(inv => inv.paymentStatus === 'paid')
                .reduce((total, inv) => total + inv.grandTotal, 0);
            const outstanding = monthInvoices
                .filter(inv => inv.paymentStatus === 'pending')
                .reduce((total, inv) => total + inv.grandTotal, 0);
            const overdue = monthInvoices
                .filter(inv => inv.paymentStatus !== 'paid' && inv.dueDate && new Date(inv.dueDate) < now)
                .reduce((total, inv) => total + inv.grandTotal, 0);
            data.push({
                period: (0, date_fns_1.format)(monthDate, 'MMM yyyy'),
                income,
                outstanding,
                overdue,
                netCashFlow: income - outstanding - overdue
            });
        }
        return data;
    }
    /**
     * Generate weekly cash flow data
     */
    generateWeeklyData(invoices, weeks) {
        const data = [];
        const now = new Date();
        for (let i = weeks - 1; i >= 0; i--) {
            const weekStart = (0, date_fns_1.subDays)(now, i * 7 + 6);
            const weekEnd = (0, date_fns_1.subDays)(now, i * 7);
            const weekInvoices = invoices.filter(invoice => {
                const invoiceDate = new Date(invoice.invoiceDate);
                return invoiceDate >= weekStart && invoiceDate <= weekEnd;
            });
            const income = weekInvoices
                .filter(inv => inv.paymentStatus === 'paid')
                .reduce((total, inv) => total + inv.grandTotal, 0);
            const outstanding = weekInvoices
                .filter(inv => inv.paymentStatus === 'pending')
                .reduce((total, inv) => total + inv.grandTotal, 0);
            const overdue = weekInvoices
                .filter(inv => inv.paymentStatus !== 'paid' && inv.dueDate && new Date(inv.dueDate) < now)
                .reduce((total, inv) => total + inv.grandTotal, 0);
            data.push({
                period: `Week ${(0, date_fns_1.format)(weekStart, 'MMM dd')} - ${(0, date_fns_1.format)(weekEnd, 'MMM dd')}`,
                income,
                outstanding,
                overdue,
                netCashFlow: income - outstanding - overdue
            });
        }
        return data;
    }
    /**
     * Get payment status breakdown
     */
    getPaymentStatusBreakdown(invoices) {
        const breakdown = {
            paid: { count: 0, amount: 0 },
            pending: { count: 0, amount: 0 },
            partial: { count: 0, amount: 0 },
            overdue: { count: 0, amount: 0 }
        };
        const now = new Date();
        invoices.forEach(invoice => {
            if (invoice.paymentStatus === 'paid') {
                breakdown.paid.count++;
                breakdown.paid.amount += invoice.grandTotal;
            }
            else if (invoice.paymentStatus === 'partial') {
                breakdown.partial.count++;
                breakdown.partial.amount += invoice.grandTotal;
            }
            else if (invoice.dueDate && new Date(invoice.dueDate) < now) {
                breakdown.overdue.count++;
                breakdown.overdue.amount += invoice.grandTotal;
            }
            else {
                breakdown.pending.count++;
                breakdown.pending.amount += invoice.grandTotal;
            }
        });
        return breakdown;
    }
    /**
     * Get top customers by revenue
     */
    getTopCustomers(invoices) {
        const customerMap = new Map();
        invoices.forEach(invoice => {
            const customerName = invoice.customer.name;
            if (!customerMap.has(customerName)) {
                customerMap.set(customerName, {
                    name: customerName,
                    totalAmount: 0,
                    invoiceCount: 0,
                    paymentTimes: []
                });
            }
            const customer = customerMap.get(customerName);
            customer.totalAmount += invoice.grandTotal;
            customer.invoiceCount++;
            if (invoice.paymentStatus === 'paid' && invoice.paymentDate && invoice.invoiceDate) {
                const paymentTime = Math.ceil((new Date(invoice.paymentDate).getTime() - new Date(invoice.invoiceDate).getTime()) /
                    (1000 * 60 * 60 * 24));
                customer.paymentTimes.push(paymentTime);
            }
        });
        return Array.from(customerMap.values())
            .map(customer => ({
            name: customer.name,
            totalAmount: customer.totalAmount,
            invoiceCount: customer.invoiceCount,
            averagePaymentTime: customer.paymentTimes.length > 0
                ? Math.round(customer.paymentTimes.reduce((a, b) => a + b, 0) / customer.paymentTimes.length)
                : 0
        }))
            .sort((a, b) => b.totalAmount - a.totalAmount)
            .slice(0, 10);
    }
    /**
     * Generate cash flow projection
     */
    generateCashFlowProjection(invoices) {
        const projection = [];
        const now = new Date();
        // Project next 3 months based on historical data
        for (let i = 1; i <= 3; i++) {
            const projectionDate = new Date(now.getFullYear(), now.getMonth() + i, 1);
            // Calculate average monthly revenue from last 6 months
            const historicalMonths = 6;
            const historicalRevenue = this.generateMonthlyData(invoices, historicalMonths)
                .reduce((total, month) => total + month.income, 0) / historicalMonths;
            // Add some variance based on trend
            const trend = this.determineCashFlowTrend(invoices);
            let multiplier = 1;
            if (trend === 'positive')
                multiplier = 1.1;
            if (trend === 'negative')
                multiplier = 0.9;
            const expectedIncome = Math.round(historicalRevenue * multiplier);
            // Determine confidence based on data consistency
            const confidence = historicalRevenue > 0 ? 'medium' : 'low';
            projection.push({
                period: (0, date_fns_1.format)(projectionDate, 'MMM yyyy'),
                expectedIncome,
                confidence
            });
        }
        return projection;
    }
}
exports.CashFlowAnalyticsService = CashFlowAnalyticsService;
exports.default = CashFlowAnalyticsService;
//# sourceMappingURL=cashFlowAnalyticsService.js.map