"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.upiWebhook = exports.manualVerifyPayment = exports.getUserPayments = exports.getInvoicePayments = exports.getPayment = exports.verifyPayment = exports.recordPayment = void 0;
const invoicePaymentService_1 = __importDefault(require("../services/invoicePaymentService"));
const Invoice_1 = __importDefault(require("../models/Invoice"));
// Record a manual payment
const recordPayment = async (req, res) => {
    try {
        const { invoiceId, amount, paymentMethod, transactionId, upiTransactionId, upiId, bankTransactionId, bankReference, customerName, customerEmail, customerPhone, notes } = req.body;
        if (!invoiceId || !amount || !paymentMethod) {
            res.status(400).json({
                success: false,
                message: 'Invoice ID, amount, and payment method are required'
            });
            return;
        }
        // Verify invoice belongs to user
        const invoice = await Invoice_1.default.findOne({
            _id: invoiceId,
            userId: req.user?._id
        });
        if (!invoice) {
            res.status(404).json({
                success: false,
                message: 'Invoice not found'
            });
            return;
        }
        const payment = await invoicePaymentService_1.default.recordPayment({
            invoiceId,
            amount: parseFloat(amount),
            paymentMethod,
            transactionId,
            upiTransactionId,
            upiId,
            bankTransactionId,
            bankReference,
            customerName,
            customerEmail,
            customerPhone,
            notes
        });
        res.status(201).json({
            success: true,
            message: 'Payment recorded successfully',
            data: {
                paymentId: payment.paymentId,
                receiptNumber: payment.receiptNumber,
                status: payment.status
            }
        });
    }
    catch (error) {
        console.error('Record payment error:', error);
        res.status(500).json({
            success: false,
            message: 'Error recording payment'
        });
    }
};
exports.recordPayment = recordPayment;
// Verify payment (webhook or manual)
const verifyPayment = async (req, res) => {
    try {
        const { paymentId, transactionId, amount, status, gatewayResponse, failureReason } = req.body;
        if (!paymentId || !transactionId || !amount || !status) {
            res.status(400).json({
                success: false,
                message: 'Payment ID, transaction ID, amount, and status are required'
            });
            return;
        }
        const payment = await invoicePaymentService_1.default.verifyPayment({
            paymentId,
            transactionId,
            amount: parseFloat(amount),
            status,
            gatewayResponse,
            failureReason
        });
        res.status(200).json({
            success: true,
            message: 'Payment verified successfully',
            data: {
                paymentId: payment.paymentId,
                receiptNumber: payment.receiptNumber,
                status: payment.status,
                isVerified: payment.isVerified
            }
        });
    }
    catch (error) {
        console.error('Verify payment error:', error);
        res.status(500).json({
            success: false,
            message: 'Error verifying payment'
        });
    }
};
exports.verifyPayment = verifyPayment;
// Get payment details
const getPayment = async (req, res) => {
    try {
        const { paymentId } = req.params;
        const payment = await invoicePaymentService_1.default.getPaymentById(paymentId);
        if (!payment) {
            res.status(404).json({
                success: false,
                message: 'Payment not found'
            });
            return;
        }
        // Check if user owns this payment
        if (payment.userId.toString() !== req.user?._id.toString()) {
            res.status(403).json({
                success: false,
                message: 'Access denied'
            });
            return;
        }
        res.status(200).json({
            success: true,
            message: 'Payment details retrieved',
            data: payment
        });
    }
    catch (error) {
        console.error('Get payment error:', error);
        res.status(500).json({
            success: false,
            message: 'Error retrieving payment'
        });
    }
};
exports.getPayment = getPayment;
// Get invoice payments
const getInvoicePayments = async (req, res) => {
    try {
        const { invoiceId } = req.params;
        // Verify invoice belongs to user
        const invoice = await Invoice_1.default.findOne({
            _id: invoiceId,
            userId: req.user?._id
        });
        if (!invoice) {
            res.status(404).json({
                success: false,
                message: 'Invoice not found'
            });
            return;
        }
        const payments = await invoicePaymentService_1.default.getInvoicePayments(invoiceId);
        res.status(200).json({
            success: true,
            message: 'Invoice payments retrieved',
            data: payments
        });
    }
    catch (error) {
        console.error('Get invoice payments error:', error);
        res.status(500).json({
            success: false,
            message: 'Error retrieving invoice payments'
        });
    }
};
exports.getInvoicePayments = getInvoicePayments;
// Get user payments
const getUserPayments = async (req, res) => {
    try {
        const { limit = 50 } = req.query;
        const userId = req.user?._id;
        const payments = await invoicePaymentService_1.default.getUserPayments(userId.toString(), parseInt(limit));
        res.status(200).json({
            success: true,
            message: 'User payments retrieved',
            data: payments
        });
    }
    catch (error) {
        console.error('Get user payments error:', error);
        res.status(500).json({
            success: false,
            message: 'Error retrieving user payments'
        });
    }
};
exports.getUserPayments = getUserPayments;
// Manual verify payment (admin)
const manualVerifyPayment = async (req, res) => {
    try {
        const { paymentId } = req.params;
        const { notes } = req.body;
        const verifiedBy = req.user?._id;
        const payment = await invoicePaymentService_1.default.manualVerifyPayment(paymentId, verifiedBy.toString(), notes);
        res.status(200).json({
            success: true,
            message: 'Payment manually verified',
            data: {
                paymentId: payment.paymentId,
                receiptNumber: payment.receiptNumber,
                status: payment.status,
                isVerified: payment.isVerified
            }
        });
    }
    catch (error) {
        console.error('Manual verify payment error:', error);
        res.status(500).json({
            success: false,
            message: 'Error manually verifying payment'
        });
    }
};
exports.manualVerifyPayment = manualVerifyPayment;
// UPI payment webhook (for future integration)
const upiWebhook = async (req, res) => {
    try {
        // This would handle UPI payment confirmations from payment gateways
        // For now, it's a placeholder
        console.log('UPI Webhook received:', req.body);
        res.status(200).json({
            success: true,
            message: 'Webhook processed'
        });
    }
    catch (error) {
        console.error('UPI webhook error:', error);
        res.status(500).json({
            success: false,
            message: 'Webhook processing failed'
        });
    }
};
exports.upiWebhook = upiWebhook;
//# sourceMappingURL=paymentController.js.map