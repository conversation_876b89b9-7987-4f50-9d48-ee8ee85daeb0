import { Request, Response, NextFunction } from 'express';
export declare const validateComplianceCreation: (req: Request, res: Response, next: NextFunction) => Response<any, Record<string, any>> | undefined;
export declare const validateComplianceUpdate: (req: Request, res: Response, next: NextFunction) => Response<any, Record<string, any>> | undefined;
//# sourceMappingURL=complianceValidation.d.ts.map