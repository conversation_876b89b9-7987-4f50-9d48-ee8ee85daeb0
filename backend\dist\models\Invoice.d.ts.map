{"version": 3, "file": "Invoice.d.ts", "sourceRoot": "", "sources": ["../../src/models/Invoice.ts"], "names": [], "mappings": "AAAA,OAAO,QAAQ,EAAE,EAAE,QAAQ,EAAU,MAAM,UAAU,CAAC;AAGtD,MAAM,WAAW,YAAY;IAC3B,WAAW,EAAE,MAAM,CAAC;IACpB,GAAG,EAAE,MAAM,CAAC;IACZ,QAAQ,EAAE,MAAM,CAAC;IACjB,IAAI,EAAE,MAAM,CAAC;IACb,IAAI,EAAE,MAAM,CAAC;IACb,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,aAAa,EAAE,MAAM,CAAC;IACtB,QAAQ,EAAE,MAAM,CAAC;IACjB,QAAQ,EAAE,MAAM,CAAC;IACjB,QAAQ,EAAE,MAAM,CAAC;IACjB,UAAU,EAAE,MAAM,CAAC;IACnB,UAAU,EAAE,MAAM,CAAC;IACnB,UAAU,EAAE,MAAM,CAAC;IACnB,WAAW,EAAE,MAAM,CAAC;CACrB;AAED,MAAM,WAAW,SAAS;IACxB,IAAI,EAAE,MAAM,CAAC;IACb,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,OAAO,EAAE;QACP,MAAM,EAAE,MAAM,CAAC;QACf,IAAI,EAAE,MAAM,CAAC;QACb,KAAK,EAAE,MAAM,CAAC;QACd,OAAO,EAAE,MAAM,CAAC;QAChB,OAAO,EAAE,MAAM,CAAC;KACjB,CAAC;CACH;AAED,MAAM,WAAW,QAAS,SAAQ,QAAQ;IACxC,aAAa,EAAE,MAAM,CAAC;IACtB,WAAW,EAAE,IAAI,CAAC;IAClB,OAAO,CAAC,EAAE,IAAI,CAAC;IACf,MAAM,EAAE,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC;IAGhC,WAAW,EAAE,KAAK,GAAG,SAAS,CAAC;IAG/B,QAAQ,EAAE,SAAS,CAAC;IAGpB,KAAK,EAAE,YAAY,EAAE,CAAC;IAGtB,QAAQ,EAAE,MAAM,CAAC;IACjB,aAAa,EAAE,MAAM,CAAC;IACtB,aAAa,EAAE,MAAM,CAAC;IACtB,SAAS,EAAE,MAAM,CAAC;IAClB,SAAS,EAAE,MAAM,CAAC;IAClB,SAAS,EAAE,MAAM,CAAC;IAClB,QAAQ,EAAE,MAAM,CAAC;IACjB,UAAU,EAAE,MAAM,CAAC;IAGnB,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,KAAK,CAAC,EAAE,MAAM,CAAC;IAGf,MAAM,EAAE,OAAO,GAAG,MAAM,GAAG,MAAM,GAAG,SAAS,GAAG,WAAW,CAAC;IAC5D,aAAa,EAAE,SAAS,GAAG,SAAS,GAAG,MAAM,CAAC;IAC9C,WAAW,CAAC,EAAE,IAAI,CAAC;IAGnB,IAAI,EAAE,MAAM,CAAC;IAGb,aAAa,CAAC,EAAE,KAAK,CAAC;QACpB,IAAI,EAAE,UAAU,GAAG,KAAK,GAAG,SAAS,CAAC;QACrC,MAAM,EAAE,IAAI,CAAC;QACb,IAAI,EAAE,MAAM,CAAC;KACd,CAAC,CAAC;IAGH,SAAS,EAAE,IAAI,CAAC;IAChB,SAAS,EAAE,IAAI,CAAC;IAGhB,YAAY,IAAI,MAAM,CAAC;IACvB,eAAe,IAAI,OAAO,CAAC;CAC5B;;;;;;AA0MD,wBAAkE"}