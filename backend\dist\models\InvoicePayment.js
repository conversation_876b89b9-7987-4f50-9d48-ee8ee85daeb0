"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importStar(require("mongoose"));
const InvoicePaymentSchema = new mongoose_1.Schema({
    invoiceId: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'Invoice',
        required: true,
        index: true
    },
    userId: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User',
        required: true,
        index: true
    },
    // Payment details
    paymentId: {
        type: String,
        required: true,
        unique: true,
        index: true
    },
    transactionId: {
        type: String,
        index: true
    },
    amount: {
        type: Number,
        required: true,
        min: 0
    },
    currency: {
        type: String,
        default: 'INR',
        uppercase: true
    },
    // Payment method
    paymentMethod: {
        type: String,
        enum: ['upi', 'bank_transfer', 'cash', 'cheque', 'card', 'other'],
        required: true
    },
    paymentGateway: {
        type: String,
        enum: ['razorpay', 'payu', 'cashfree', 'manual']
    },
    // UPI specific
    upiTransactionId: String,
    upiId: String,
    // Bank transfer specific
    bankTransactionId: String,
    bankReference: String,
    // Status
    status: {
        type: String,
        enum: ['pending', 'completed', 'failed', 'refunded', 'cancelled'],
        default: 'pending',
        index: true
    },
    paymentDate: {
        type: Date,
        default: Date.now
    },
    // Gateway response
    gatewayResponse: mongoose_1.Schema.Types.Mixed,
    failureReason: String,
    // Verification
    isVerified: {
        type: Boolean,
        default: false
    },
    verifiedAt: Date,
    verifiedBy: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User'
    },
    // Receipt
    receiptNumber: {
        type: String,
        required: true,
        unique: true
    },
    receiptGenerated: {
        type: Boolean,
        default: false
    },
    receiptUrl: String,
    // Customer info
    customerName: String,
    customerEmail: String,
    customerPhone: String,
    // Metadata
    notes: String,
    metadata: mongoose_1.Schema.Types.Mixed
}, {
    timestamps: true
});
// Indexes for better query performance
InvoicePaymentSchema.index({ invoiceId: 1, status: 1 });
InvoicePaymentSchema.index({ userId: 1, paymentDate: -1 });
InvoicePaymentSchema.index({ paymentMethod: 1, status: 1 });
InvoicePaymentSchema.index({ createdAt: -1 });
// Generate receipt number before saving
InvoicePaymentSchema.pre('save', async function (next) {
    if (this.isNew && !this.receiptNumber) {
        const count = await mongoose_1.default.model('InvoicePayment').countDocuments();
        const year = new Date().getFullYear();
        const month = String(new Date().getMonth() + 1).padStart(2, '0');
        this.receiptNumber = `RCP-${year}${month}-${String(count + 1).padStart(6, '0')}`;
    }
    next();
});
exports.default = mongoose_1.default.model('InvoicePayment', InvoicePaymentSchema);
//# sourceMappingURL=InvoicePayment.js.map