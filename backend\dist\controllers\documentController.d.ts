import { Request, Response } from 'express';
import multer from 'multer';
export declare const upload: multer.Multer;
export declare const uploadDocument: (req: Request, res: Response) => Promise<void>;
export declare const uploadMultipleDocuments: (req: Request, res: Response) => Promise<void>;
export declare const getUserDocuments: (req: Request, res: Response) => Promise<void>;
export declare const getDocument: (req: Request, res: Response) => Promise<void>;
export declare const downloadDocument: (req: Request, res: Response) => Promise<void>;
export declare const updateDocument: (req: Request, res: Response) => Promise<void>;
export declare const analyzeDocument: (req: Request, res: Response) => Promise<void>;
export declare const deleteDocument: (req: Request, res: Response) => Promise<void>;
export declare const getDocumentStats: (req: Request, res: Response) => Promise<void>;
export declare const parseDocument: (req: Request, res: Response) => Promise<void>;
export declare const verifyDocumentOnBlockchain: (req: Request, res: Response) => Promise<void>;
export declare const storeDocumentOnBlockchain: (req: Request, res: Response) => Promise<void>;
export declare const getBlockchainStatus: (req: Request, res: Response) => Promise<void>;
//# sourceMappingURL=documentController.d.ts.map