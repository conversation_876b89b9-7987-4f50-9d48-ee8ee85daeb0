"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const auth_1 = require("../middleware/auth");
const usageTracking_1 = require("../middleware/usageTracking");
const teamController_1 = require("../controllers/teamController");
const router = express_1.default.Router();
// All routes require authentication
router.use(auth_1.authenticate);
/**
 * @route   POST /api/team/invite
 * @desc    Invite a team member
 * @access  Private (Business plan + team management permission)
 */
router.post('/invite', (0, usageTracking_1.checkFeatureAccess)('multiUser'), teamController_1.inviteTeamMember);
/**
 * @route   GET /api/team/members
 * @desc    Get team members
 * @access  Private (Business plan)
 */
router.get('/members', (0, usageTracking_1.checkFeatureAccess)('multiUser'), teamController_1.getTeamMembers);
/**
 * @route   PUT /api/team/members/:memberId
 * @desc    Update team member
 * @access  Private (Business plan + team management permission)
 */
router.put('/members/:memberId', (0, usageTracking_1.checkFeatureAccess)('multiUser'), teamController_1.updateTeamMember);
/**
 * @route   DELETE /api/team/members/:memberId
 * @desc    Remove team member
 * @access  Private (Business plan + team management permission)
 */
router.delete('/members/:memberId', (0, usageTracking_1.checkFeatureAccess)('multiUser'), teamController_1.removeTeamMember);
/**
 * @route   POST /api/team/accept-invitation
 * @desc    Accept team invitation
 * @access  Private
 */
router.post('/accept-invitation', teamController_1.acceptInvitation);
/**
 * @route   GET /api/team/memberships
 * @desc    Get user's team memberships
 * @access  Private
 */
router.get('/memberships', teamController_1.getUserMemberships);
/**
 * @route   GET /api/team/permissions
 * @desc    Check user permissions
 * @access  Private
 */
router.get('/permissions', teamController_1.checkPermissions);
/**
 * @route   GET /api/team/stats
 * @desc    Get team statistics
 * @access  Private (Business plan)
 */
router.get('/stats', (0, usageTracking_1.checkFeatureAccess)('multiUser'), teamController_1.getTeamStats);
exports.default = router;
//# sourceMappingURL=team.js.map