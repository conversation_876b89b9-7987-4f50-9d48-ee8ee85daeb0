"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const auth_1 = require("../middleware/auth");
const securityAuditService_1 = require("../services/securityAuditService");
const securityMonitoringService_1 = require("../services/securityMonitoringService");
const security_1 = require("../config/security");
const router = express_1.default.Router();
// All security routes require authentication and admin role
router.use(auth_1.authenticate);
router.use((req, res, next) => {
    if (req.user?.role !== 'admin') {
        return res.status(403).json({
            success: false,
            message: 'Access denied. Admin role required.',
            error: 'INSUFFICIENT_PRIVILEGES'
        });
    }
    next();
});
/**
 * @route   GET /api/security/dashboard
 * @desc    Get security dashboard overview
 * @access  Admin only
 */
router.get('/dashboard', async (req, res) => {
    try {
        const [auditResult, securityStats] = await Promise.all([
            securityAuditService_1.securityAudit.performSecurityAudit(),
            securityMonitoringService_1.securityMonitoring.getSecurityStats('day')
        ]);
        const dashboard = {
            overview: {
                securityScore: auditResult.overallScore,
                lastAuditDate: auditResult.timestamp,
                criticalVulnerabilities: auditResult.vulnerabilities.filter(v => v.severity === 'CRITICAL').length,
                highVulnerabilities: auditResult.vulnerabilities.filter(v => v.severity === 'HIGH').length,
                totalRecommendations: auditResult.recommendations.length
            },
            recentEvents: securityStats,
            topVulnerabilities: auditResult.vulnerabilities
                .sort((a, b) => (b.cvss || 0) - (a.cvss || 0))
                .slice(0, 5),
            urgentRecommendations: auditResult.recommendations
                .filter(r => r.priority === 'CRITICAL' || r.priority === 'HIGH')
                .slice(0, 5),
            categoryScores: auditResult.categories
        };
        res.json({
            success: true,
            data: dashboard
        });
    }
    catch (error) {
        console.error('Security dashboard error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to load security dashboard',
            error: 'DASHBOARD_ERROR'
        });
    }
});
/**
 * @route   POST /api/security/audit
 * @desc    Trigger a new security audit
 * @access  Admin only
 */
router.post('/audit', async (req, res) => {
    try {
        const auditResult = await securityAuditService_1.securityAudit.performSecurityAudit();
        res.json({
            success: true,
            message: 'Security audit completed successfully',
            data: auditResult
        });
    }
    catch (error) {
        console.error('Security audit error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to perform security audit',
            error: 'AUDIT_ERROR'
        });
    }
});
/**
 * @route   GET /api/security/vulnerabilities
 * @desc    Get detailed vulnerability report
 * @access  Admin only
 */
router.get('/vulnerabilities', async (req, res) => {
    try {
        const auditResult = await securityAuditService_1.securityAudit.performSecurityAudit();
        const vulnerabilityReport = {
            summary: {
                total: auditResult.vulnerabilities.length,
                critical: auditResult.vulnerabilities.filter(v => v.severity === 'CRITICAL').length,
                high: auditResult.vulnerabilities.filter(v => v.severity === 'HIGH').length,
                medium: auditResult.vulnerabilities.filter(v => v.severity === 'MEDIUM').length,
                low: auditResult.vulnerabilities.filter(v => v.severity === 'LOW').length
            },
            vulnerabilities: auditResult.vulnerabilities.sort((a, b) => (b.cvss || 0) - (a.cvss || 0)),
            lastScanDate: auditResult.timestamp
        };
        res.json({
            success: true,
            data: vulnerabilityReport
        });
    }
    catch (error) {
        console.error('Vulnerability report error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to generate vulnerability report',
            error: 'VULNERABILITY_REPORT_ERROR'
        });
    }
});
/**
 * @route   GET /api/security/recommendations
 * @desc    Get security recommendations
 * @access  Admin only
 */
router.get('/recommendations', async (req, res) => {
    try {
        const auditResult = await securityAuditService_1.securityAudit.performSecurityAudit();
        const recommendationReport = {
            summary: {
                total: auditResult.recommendations.length,
                critical: auditResult.recommendations.filter(r => r.priority === 'CRITICAL').length,
                high: auditResult.recommendations.filter(r => r.priority === 'HIGH').length,
                medium: auditResult.recommendations.filter(r => r.priority === 'MEDIUM').length,
                low: auditResult.recommendations.filter(r => r.priority === 'LOW').length
            },
            recommendations: auditResult.recommendations.sort((a, b) => {
                const priorityOrder = { 'CRITICAL': 4, 'HIGH': 3, 'MEDIUM': 2, 'LOW': 1 };
                return priorityOrder[b.priority] - priorityOrder[a.priority];
            }),
            lastScanDate: auditResult.timestamp
        };
        res.json({
            success: true,
            data: recommendationReport
        });
    }
    catch (error) {
        console.error('Recommendations report error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to generate recommendations report',
            error: 'RECOMMENDATIONS_REPORT_ERROR'
        });
    }
});
/**
 * @route   GET /api/security/events
 * @desc    Get security events log
 * @access  Admin only
 */
router.get('/events', async (req, res) => {
    try {
        const { timeframe = 'day', limit = 100, severity } = req.query;
        const events = await securityMonitoringService_1.securityMonitoring.getSecurityStats(timeframe);
        res.json({
            success: true,
            data: {
                events,
                filters: {
                    timeframe,
                    limit,
                    severity
                }
            }
        });
    }
    catch (error) {
        console.error('Security events error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve security events',
            error: 'EVENTS_ERROR'
        });
    }
});
/**
 * @route   POST /api/security/encrypt
 * @desc    Encrypt sensitive data (utility endpoint)
 * @access  Admin only
 */
router.post('/encrypt', async (req, res) => {
    try {
        const { data } = req.body;
        if (!data) {
            return res.status(400).json({
                success: false,
                message: 'Data to encrypt is required',
                error: 'MISSING_DATA'
            });
        }
        const encrypted = security_1.EncryptionService.encrypt(data);
        res.json({
            success: true,
            message: 'Data encrypted successfully',
            data: {
                encrypted: encrypted.encrypted,
                iv: encrypted.iv,
                tag: encrypted.tag
            }
        });
    }
    catch (error) {
        console.error('Encryption error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to encrypt data',
            error: 'ENCRYPTION_ERROR'
        });
    }
});
/**
 * @route   POST /api/security/generate-api-key
 * @desc    Generate secure API key
 * @access  Admin only
 */
router.post('/generate-api-key', async (req, res) => {
    try {
        const apiKey = security_1.EncryptionService.generateApiKey();
        const hashedKey = security_1.EncryptionService.hash(apiKey);
        res.json({
            success: true,
            message: 'API key generated successfully',
            data: {
                apiKey,
                hashedKey,
                warning: 'Store this API key securely. It will not be shown again.'
            }
        });
    }
    catch (error) {
        console.error('API key generation error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to generate API key',
            error: 'API_KEY_GENERATION_ERROR'
        });
    }
});
/**
 * @route   GET /api/security/health
 * @desc    Get security health status
 * @access  Admin only
 */
router.get('/health', async (req, res) => {
    try {
        const healthChecks = {
            jwtSecret: !!process.env.JWT_SECRET && process.env.JWT_SECRET.length >= 32,
            bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS || '12') >= 12,
            httpsEnabled: process.env.NODE_ENV === 'production' ? !!process.env.HTTPS_ENABLED : true,
            rateLimitingEnabled: true, // We know this is enabled from our middleware
            inputSanitizationEnabled: true, // We know this is enabled from our middleware
            securityHeadersEnabled: true, // We know this is enabled from our middleware
            loggingEnabled: true, // We know this is enabled from our monitoring service
            encryptionConfigured: true // We know this is configured from our security config
        };
        const healthScore = Object.values(healthChecks).filter(Boolean).length / Object.keys(healthChecks).length * 100;
        res.json({
            success: true,
            data: {
                overallHealth: Math.round(healthScore),
                checks: healthChecks,
                timestamp: new Date().toISOString()
            }
        });
    }
    catch (error) {
        console.error('Security health check error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to perform security health check',
            error: 'HEALTH_CHECK_ERROR'
        });
    }
});
exports.default = router;
//# sourceMappingURL=securityRoutes.js.map