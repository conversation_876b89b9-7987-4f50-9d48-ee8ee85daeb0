'use client';

import React, { createContext, useContext, ReactNode } from 'react';
import { useRecaptcha, getRecaptchaSiteKey, RecaptchaAction } from '../../hooks/useRecaptcha';

interface RecaptchaContextType {
  isLoaded: boolean;
  isLoading: boolean;
  executeRecaptcha: (action: RecaptchaAction) => Promise<string | null>;
}

const RecaptchaContext = createContext<RecaptchaContextType | undefined>(undefined);

interface RecaptchaProviderProps {
  children: ReactNode;
}

export const RecaptchaProvider: React.FC<RecaptchaProviderProps> = ({ children }) => {
  const siteKey = getRecaptchaSiteKey();
  
  // Use a default action for initialization, actual action will be passed when executing
  const { isLoaded, isLoading, executeRecaptcha: baseExecuteRecaptcha } = useRecaptcha({
    siteKey,
    action: 'init',
  });

  const executeRecaptcha = async (action: RecaptchaAction): Promise<string | null> => {
    if (!isLoaded || !window.grecaptcha) {
      console.warn('reCAPTCHA not loaded yet');
      return null;
    }

    try {
      const token = await window.grecaptcha.execute(siteKey, { action });
      return token;
    } catch (error) {
      console.error('reCAPTCHA execution failed:', error);
      return null;
    }
  };

  const value: RecaptchaContextType = {
    isLoaded,
    isLoading,
    executeRecaptcha,
  };

  return (
    <RecaptchaContext.Provider value={value}>
      {children}
    </RecaptchaContext.Provider>
  );
};

export const useRecaptchaContext = (): RecaptchaContextType => {
  const context = useContext(RecaptchaContext);
  if (context === undefined) {
    throw new Error('useRecaptchaContext must be used within a RecaptchaProvider');
  }
  return context;
};

// Higher-order component to wrap forms with reCAPTCHA
export const withRecaptcha = <P extends object>(
  Component: React.ComponentType<P>
): React.FC<P> => {
  return (props: P) => (
    <RecaptchaProvider>
      <Component {...props} />
    </RecaptchaProvider>
  );
};

// Badge component to show reCAPTCHA branding (required by Google)
export const RecaptchaBadge: React.FC<{ className?: string }> = ({ className = '' }) => {
  return (
    <div className={`text-xs text-gray-500 mt-4 ${className}`}>
      This site is protected by reCAPTCHA and the Google{' '}
      <a
        href="https://policies.google.com/privacy"
        target="_blank"
        rel="noopener noreferrer"
        className="text-blue-600 hover:text-blue-800 underline"
      >
        Privacy Policy
      </a>{' '}
      and{' '}
      <a
        href="https://policies.google.com/terms"
        target="_blank"
        rel="noopener noreferrer"
        className="text-blue-600 hover:text-blue-800 underline"
      >
        Terms of Service
      </a>{' '}
      apply.
    </div>
  );
};
