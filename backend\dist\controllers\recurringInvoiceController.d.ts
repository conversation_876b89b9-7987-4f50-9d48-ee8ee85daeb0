import { Request, Response } from 'express';
export declare const getRecurringInvoices: (req: Request, res: Response) => Promise<void>;
export declare const getRecurringInvoice: (req: Request, res: Response) => Promise<void>;
export declare const createRecurringInvoice: (req: Request, res: Response) => Promise<void>;
export declare const updateRecurringInvoice: (req: Request, res: Response) => Promise<void>;
export declare const deleteRecurringInvoice: (req: Request, res: Response) => Promise<void>;
export declare const toggleRecurringInvoice: (req: Request, res: Response) => Promise<void>;
export declare const generateInvoiceNow: (req: Request, res: Response) => Promise<void>;
//# sourceMappingURL=recurringInvoiceController.d.ts.map