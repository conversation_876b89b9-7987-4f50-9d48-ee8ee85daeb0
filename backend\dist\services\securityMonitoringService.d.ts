import { Request } from 'express';
import { IUser } from '../models/User';
export declare enum SecurityEventType {
    LOGIN_SUCCESS = "LOGIN_SUCCESS",
    LOGIN_FAILURE = "LOGIN_FAILURE",
    BRUTE_FORCE_ATTEMPT = "BRUTE_FORCE_ATTEMPT",
    SUSPICIOUS_ACTIVITY = "SUSPICIOUS_ACTIVITY",
    RATE_LIMIT_EXCEEDED = "RATE_LIMIT_EXCEEDED",
    UNAUTHORIZED_ACCESS = "UNAUTHORIZED_ACCESS",
    DATA_BREACH_ATTEMPT = "DATA_BREACH_ATTEMPT",
    API_KEY_MISUSE = "API_KEY_MISUSE",
    PRIVILEGE_ESCALATION = "PRIVILEGE_ESCALATION",
    ACCOUNT_LOCKOUT = "ACCOUNT_LOCKOUT",
    PASSWORD_RESET = "PASSWORD_RESET",
    EMAIL_VERIFICATION = "EMAIL_VERIFICATION",
    OTP_FAILURE = "OTP_FAILURE",
    FILE_UPLOAD_VIOLATION = "FILE_UPLOAD_VIOLATION",
    SQL_INJECTION_ATTEMPT = "SQL_INJECTION_ATTEMPT",
    XSS_ATTEMPT = "XSS_ATTEMPT"
}
export declare enum SecuritySeverity {
    LOW = "LOW",
    MEDIUM = "MEDIUM",
    HIGH = "HIGH",
    CRITICAL = "CRITICAL"
}
export interface SecurityEvent {
    type: SecurityEventType;
    severity: SecuritySeverity;
    message: string;
    ip: string;
    userAgent?: string;
    userId?: string;
    email?: string;
    path?: string;
    method?: string;
    payload?: any;
    timestamp: Date;
    metadata?: Record<string, any>;
}
declare class SecurityMonitoringService {
    private logger;
    private alertThresholds;
    private eventCounts;
    constructor();
    private initializeLogger;
    private initializeAlertThresholds;
    /**
     * Log a security event
     */
    logSecurityEvent(event: SecurityEvent): void;
    /**
     * Log authentication events
     */
    logAuthEvent(type: SecurityEventType.LOGIN_SUCCESS | SecurityEventType.LOGIN_FAILURE, req: Request, user?: IUser, additionalInfo?: Record<string, any>): void;
    /**
     * Log suspicious activity
     */
    logSuspiciousActivity(req: Request, reason: string, severity?: SecuritySeverity, additionalData?: Record<string, any>): void;
    /**
     * Log rate limiting events
     */
    logRateLimitEvent(req: Request, limitType: string): void;
    /**
     * Log API key misuse
     */
    logApiKeyMisuse(req: Request, reason: string, keyId?: string): void;
    /**
     * Log potential injection attacks
     */
    logInjectionAttempt(req: Request, type: SecurityEventType.SQL_INJECTION_ATTEMPT | SecurityEventType.XSS_ATTEMPT, detectedPattern: string): void;
    /**
     * Get security statistics
     */
    getSecurityStats(timeframe?: 'hour' | 'day' | 'week'): Promise<any>;
    private getLogLevel;
    private checkForAlerts;
    private triggerAlert;
}
export declare const securityMonitoring: SecurityMonitoringService;
export default SecurityMonitoringService;
//# sourceMappingURL=securityMonitoringService.d.ts.map