import { Request, Response, NextFunction } from 'express';
export declare const validateNotificationCreation: (req: Request, res: Response, next: NextFunction) => Response<any, Record<string, any>> | undefined;
export declare const validateNotificationPreferences: (req: Request, res: Response, next: NextFunction) => Response<any, Record<string, any>> | undefined;
//# sourceMappingURL=notificationValidation.d.ts.map