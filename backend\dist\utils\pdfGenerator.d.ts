import { IInvoice } from '../models/Invoice';
import { IUser } from '../models/User';
export interface InvoicePDFData {
    invoice: IInvoice;
    seller: IUser;
}
/**
 * Generate HTML template for professional GST invoice
 */
export declare const generateInvoiceHTML: (data: InvoicePDFData) => Promise<string>;
/**
 * Generate PDF from invoice data with optimized single-page layout
 */
export declare const generateInvoicePDF: (data: InvoicePDFData) => Promise<Buffer>;
export declare const closePDFBrowser: () => Promise<void>;
//# sourceMappingURL=pdfGenerator.d.ts.map