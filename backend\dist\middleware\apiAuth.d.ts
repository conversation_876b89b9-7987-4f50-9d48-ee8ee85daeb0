import { Request, Response, NextFunction } from 'express';
export interface ApiAuthRequest extends Request {
    apiKey?: {
        userId: string;
        keyId: string;
        permissions: any;
    };
}
/**
 * Middleware to authenticate API requests using API keys
 */
export declare const authenticateApiKey: (req: ApiAuthRequest, res: Response, next: NextFunction) => Promise<Response<any, Record<string, any>> | undefined>;
/**
 * Middleware to check API key permissions for specific resource and action
 */
export declare const checkApiPermission: (resource: string, action: string) => (req: ApiAuthRequest, res: Response, next: NextFunction) => Response<any, Record<string, any>> | undefined;
/**
 * Middleware that works with both JWT and API key authentication
 */
export declare const authenticateFlexible: (req: any, res: Response, next: NextFunction) => Promise<any>;
//# sourceMappingURL=apiAuth.d.ts.map