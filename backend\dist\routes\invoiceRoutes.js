"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const invoiceController_1 = require("../controllers/invoiceController");
const invoiceCalculationController_1 = require("../controllers/invoiceCalculationController");
const auth_1 = require("../middleware/auth");
const invoiceValidation_1 = require("../middleware/invoiceValidation");
const usageTracking_1 = require("../middleware/usageTracking");
const router = express_1.default.Router();
// All routes require authentication
router.use(auth_1.authenticate);
// Calculation and utility endpoints (must come before /:id routes)
router.post('/calculate', invoiceCalculationController_1.calculateInvoiceTotals);
router.get('/gst-rates', invoiceCalculationController_1.getGSTRates);
router.post('/validate-gst', invoiceCalculationController_1.validateGSTNumber);
router.get('/hsn-codes', invoiceCalculationController_1.getCommonHSNCodes);
router.get('/states', invoiceCalculationController_1.getIndianStates);
// Invoice CRUD operations
router.post('/', (0, usageTracking_1.checkUsageLimit)('invoice'), invoiceValidation_1.validateInvoiceCreation, invoiceController_1.createInvoice);
router.get('/', invoiceController_1.getInvoices);
router.get('/:id', invoiceController_1.getInvoice);
router.put('/:id', invoiceValidation_1.validateInvoiceUpdate, invoiceController_1.updateInvoice);
router.delete('/:id', invoiceController_1.deleteInvoice);
// PDF generation
router.get('/:id/pdf', invoiceController_1.downloadInvoicePDF);
// Email sending
router.post('/:id/send-email', invoiceController_1.sendInvoiceEmail);
// Email queue status (development only)
router.get('/email-queue-status', invoiceController_1.getEmailQueueStatus);
// Performance statistics (development only)
router.get('/performance-stats', invoiceController_1.getPerformanceStats);
// Blockchain integrity verification (Business plan feature)
router.get('/:id/verify', (0, usageTracking_1.checkFeatureAccess)('apiAccess'), invoiceController_1.verifyInvoiceIntegrity);
exports.default = router;
//# sourceMappingURL=invoiceRoutes.js.map