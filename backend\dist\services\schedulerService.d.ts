declare class SchedulerService {
    private isRunning;
    start(): void;
    stop(): void;
    checkComplianceReminders(): Promise<void>;
    sendComplianceReminder(userCompliance: any, daysUntilDue: number): Promise<void>;
    processScheduledNotifications(): Promise<void>;
    processNotification(notification: any): Promise<void>;
    private generateComplianceEmailHTML;
    private generateComplianceEmailText;
    triggerComplianceCheck(): Promise<void>;
    triggerNotificationProcessing(): Promise<void>;
}
declare const _default: SchedulerService;
export default _default;
//# sourceMappingURL=schedulerService.d.ts.map