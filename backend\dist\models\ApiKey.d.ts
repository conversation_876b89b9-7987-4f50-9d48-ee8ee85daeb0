import mongoose, { Document } from 'mongoose';
export interface IApiKey extends Document {
    userId: mongoose.Types.ObjectId;
    name: string;
    keyId: string;
    hashedKey: string;
    permissions: {
        invoices: {
            read: boolean;
            create: boolean;
            update: boolean;
            delete: boolean;
        };
        customers: {
            read: boolean;
            create: boolean;
            update: boolean;
            delete: boolean;
        };
        documents: {
            read: boolean;
            create: boolean;
        };
    };
    isActive: boolean;
    lastUsed?: Date;
    usageCount: number;
    rateLimit: {
        requestsPerMinute: number;
        requestsPerHour: number;
        requestsPerDay: number;
    };
    expiresAt?: Date;
    createdAt: Date;
    updatedAt: Date;
    generateKey(): {
        keyId: string;
        apiKey: string;
    };
    verifyKey(providedKey: string): boolean;
}
declare const _default: mongoose.Model<IApiKey, {}, {}, {}, mongoose.Document<unknown, {}, IApiKey, {}> & IApi<PERSON>ey & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
export default _default;
//# sourceMappingURL=ApiKey.d.ts.map