"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const authController_1 = require("../controllers/authController");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
// import { advancedBruteForceProtection } from '../middleware/security';
// import { handleValidationErrors, commonValidations } from '../middleware/inputSanitization';
const router = express_1.default.Router();
// Public routes (temporarily using basic validation)
router.post('/register', validation_1.validateRegistration, authController_1.register);
router.post('/login', validation_1.validateLogin, authController_1.login);
router.post('/verify-email', authController_1.verifyEmail);
// UPI validation routes (public)
router.post('/validate-upi', authController_1.validateUPI);
router.get('/upi-providers', authController_1.getUPIProviders);
// Protected routes
router.use(auth_1.authenticate); // All routes below require authentication
router.get('/profile', authController_1.getProfile);
router.put('/profile', validation_1.validateProfileUpdate, authController_1.updateProfile);
router.post('/logout', authController_1.logout);
router.post('/send-verification', authController_1.sendEmailVerification);
exports.default = router;
//# sourceMappingURL=authRoutes.js.map