{"version": 3, "file": "emailQueueService.js", "sourceRoot": "", "sources": ["../../src/services/emailQueueService.ts"], "names": [], "mappings": ";;AAAA,iDAAiD;AAcjD,MAAM,iBAAiB;IAMrB,MAAM,CAAC,WAAW;QAChB,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,CAAC;YAChC,iBAAiB,CAAC,QAAQ,GAAG,IAAI,iBAAiB,EAAE,CAAC;QACvD,CAAC;QACD,OAAO,iBAAiB,CAAC,QAAQ,CAAC;IACpC,CAAC;IAED;QAXQ,UAAK,GAAe,EAAE,CAAC;QACvB,eAAU,GAAG,KAAK,CAAC;QACnB,uBAAkB,GAA0B,IAAI,CAAC;QAUvD,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CACrB,EAAU,EACV,WAAgB,EAChB,SAAiB,EACjB,WAAoB,KAAK;QAEzB,MAAM,GAAG,GAAa;YACpB,EAAE,EAAE,IAAI,CAAC,aAAa,EAAE;YACxB,IAAI,EAAE,SAAS;YACf,EAAE;YACF,IAAI,EAAE,WAAW;YACjB,SAAS;YACT,OAAO,EAAE,CAAC;YACV,UAAU,EAAE,CAAC;YACb,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,IAAI,QAAQ,EAAE,CAAC;YACb,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,4BAA4B;QACvD,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACvB,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,+BAA+B,GAAG,CAAC,EAAE,QAAQ,EAAE,iBAAiB,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;QAClG,OAAO,GAAG,CAAC,EAAE,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CACd,IAAsB,EACtB,EAAU,EACV,IAAS,EACT,SAAkB;QAElB,MAAM,GAAG,GAAa;YACpB,EAAE,EAAE,IAAI,CAAC,aAAa,EAAE;YACxB,IAAI;YACJ,EAAE;YACF,IAAI;YACJ,SAAS;YACT,OAAO,EAAE,CAAC;YACV,UAAU,EAAE,CAAC;YACb,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACrB,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,cAAc,GAAG,CAAC,EAAE,QAAQ,EAAE,iBAAiB,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;QAClG,OAAO,GAAG,CAAC,EAAE,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,eAAe;QACrB,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC5B,OAAO;QACT,CAAC;QAED,IAAI,CAAC,kBAAkB,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;YAC/C,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC9C,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;YAC5B,CAAC;QACH,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,uBAAuB;QAEjC,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,YAAY;QACxB,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC/C,OAAO;QACT,CAAC;QAED,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QAEvB,IAAI,CAAC;YACH,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;YAC/B,IAAI,CAAC,GAAG,EAAE,CAAC;gBACT,OAAO;YACT,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,2BAA2B,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,IAAI,SAAS,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;YAC7E,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE7B,MAAM,YAAY,GAAG,IAAA,8BAAe,GAAE,CAAC;YACvC,IAAI,OAAO,GAAG,KAAK,CAAC;YAEpB,IAAI,CAAC;gBACH,QAAQ,GAAG,CAAC,IAAI,EAAE,CAAC;oBACjB,KAAK,SAAS;wBACZ,OAAO,GAAG,MAAM,YAAY,CAAC,gBAAgB,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,SAAU,CAAC,CAAC;wBAChF,MAAM;oBACR,KAAK,SAAS;wBACZ,OAAO,GAAG,MAAM,YAAY,CAAC,gBAAgB,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,SAAU,CAAC,CAAC;wBAChF,MAAM;oBACR,KAAK,SAAS;wBACZ,OAAO,GAAG,MAAM,YAAY,CAAC,gBAAgB,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;wBACzE,MAAM;oBACR,KAAK,cAAc;wBACjB,OAAO,GAAG,MAAM,YAAY,CAAC,0BAA0B,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;wBACnG,MAAM;oBACR,KAAK,UAAU;wBACb,OAAO,GAAG,MAAM,YAAY,CAAC,mBAAmB,CAC9C,GAAG,CAAC,EAAE,EACN,GAAG,CAAC,IAAI,CAAC,aAAa,EACtB,GAAG,CAAC,IAAI,CAAC,OAAO,EAChB,GAAG,CAAC,IAAI,CAAC,MAAM,EACf,GAAG,CAAC,IAAI,CAAC,YAAY,CACtB,CAAC;wBACF,MAAM;oBACR;wBACE,OAAO,CAAC,KAAK,CAAC,6BAA6B,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;wBACvD,OAAO,GAAG,KAAK,CAAC;gBACpB,CAAC;gBAED,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBAE3B,IAAI,OAAO,EAAE,CAAC;oBACZ,OAAO,CAAC,GAAG,CAAC,eAAe,GAAG,CAAC,EAAE,8BAA8B,OAAO,GAAG,SAAS,IAAI,CAAC,CAAC;gBAC1F,CAAC;qBAAM,CAAC;oBACN,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;gBAC1C,CAAC;YAEH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,eAAe,GAAG,CAAC,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;gBAEtD,cAAc;gBACd,IAAI,GAAG,CAAC,OAAO,GAAG,GAAG,CAAC,UAAU,EAAE,CAAC;oBACjC,GAAG,CAAC,OAAO,EAAE,CAAC;oBACd,GAAG,CAAC,WAAW,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,sBAAsB;oBACtF,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,qBAAqB;oBAC3C,OAAO,CAAC,GAAG,CAAC,yBAAyB,GAAG,CAAC,EAAE,aAAa,GAAG,CAAC,OAAO,IAAI,GAAG,CAAC,UAAU,GAAG,CAAC,CAAC;gBAC5F,CAAC;qBAAM,CAAC;oBACN,OAAO,CAAC,KAAK,CAAC,gBAAgB,GAAG,CAAC,EAAE,6BAA6B,GAAG,CAAC,UAAU,UAAU,CAAC,CAAC;gBAC7F,CAAC;YACH,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;QAC1D,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QAC1B,CAAC;IACH,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO;YACL,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM;YAC5B,UAAU,EAAE,IAAI,CAAC,UAAU;SAC5B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,UAAU;QACR,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;QAChB,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,IAAI;QACF,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC5B,aAAa,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACvC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;YAC/B,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;QAClD,CAAC;IACH,CAAC;IAEO,aAAa;QACnB,OAAO,SAAS,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IAC1E,CAAC;CACF;AAED,kBAAe,iBAAiB,CAAC"}