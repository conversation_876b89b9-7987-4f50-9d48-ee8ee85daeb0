{"version": 3, "file": "resourceAuth.js", "sourceRoot": "", "sources": ["../../src/middleware/resourceAuth.ts"], "names": [], "mappings": ";;;AACA,qFAAgH;AAEhH;;GAEG;AACI,MAAM,sBAAsB,GAAG,CAAC,aAAkB,EAAE,kBAA0B,IAAI,EAAE,EAAE;IAC3F,OAAO,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAiB,EAAE;QAC9E,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,GAAG,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;YAC/C,MAAM,MAAM,GAAI,GAAW,CAAC,IAAI,EAAE,EAAE,CAAC;YAErC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,yBAAyB;oBAClC,KAAK,EAAE,yBAAyB;iBACjC,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,oBAAoB;YACpB,MAAM,QAAQ,GAAG,MAAM,aAAa,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;YAE1D,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,oBAAoB;oBAC7B,KAAK,EAAE,oBAAoB;iBAC5B,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,kBAAkB;YAClB,MAAM,cAAc,GAAG,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,SAAS,IAAI,QAAQ,CAAC,KAAK,CAAC;YAE/E,IAAI,cAAc,CAAC,QAAQ,EAAE,KAAK,MAAM,CAAC,QAAQ,EAAE,EAAE,CAAC;gBACpD,kCAAkC;gBAClC,8CAAkB,CAAC,gBAAgB,CAAC;oBAClC,IAAI,EAAE,6CAAiB,CAAC,mBAAmB;oBAC3C,QAAQ,EAAE,4CAAgB,CAAC,IAAI;oBAC/B,OAAO,EAAE,2CAA2C,UAAU,EAAE;oBAChE,EAAE,EAAE,GAAG,CAAC,EAAE,IAAI,SAAS;oBACvB,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;oBAChC,MAAM,EAAE,MAAM;oBACd,IAAI,EAAE,GAAG,CAAC,IAAI;oBACd,MAAM,EAAE,GAAG,CAAC,MAAM;oBAClB,SAAS,EAAE,IAAI,IAAI,EAAE;oBACrB,QAAQ,EAAE;wBACR,UAAU;wBACV,YAAY,EAAE,aAAa,CAAC,SAAS;wBACrC,eAAe,EAAE,MAAM;wBACvB,YAAY,EAAE,cAAc,CAAC,QAAQ,EAAE;qBACxC;iBACF,CAAC,CAAC;gBAEH,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,8CAA8C;oBACvD,KAAK,EAAE,wBAAwB;iBAChC,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,gDAAgD;YAC/C,GAAW,CAAC,QAAQ,GAAG,QAAQ,CAAC;YACjC,IAAI,EAAE,CAAC;QACT,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YACxD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,4CAA4C;gBACrD,KAAK,EAAE,qBAAqB;aAC7B,CAAC,CAAC;QACL,CAAC;IACH,CAAC,CAAC;AACJ,CAAC,CAAC;AAtEW,QAAA,sBAAsB,0BAsEjC;AAEF;;GAEG;AACI,MAAM,uBAAuB,GAAG,CAAC,aAAkB,EAAE,kBAA0B,EAAE,EAAE;IACxF,OAAO,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAiB,EAAE;QAC9E,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;YACjC,MAAM,MAAM,GAAI,GAAW,CAAC,IAAI,EAAE,EAAE,CAAC;YAErC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,yBAAyB;oBAClC,KAAK,EAAE,yBAAyB;iBACjC,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,oBAAoB;YACpB,MAAM,QAAQ,GAAG,MAAM,aAAa,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;YAE1D,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,oBAAoB;oBAC7B,KAAK,EAAE,oBAAoB;iBAC5B,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,yBAAyB;YACzB,MAAM,cAAc,GAAG,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,SAAS,IAAI,QAAQ,CAAC,KAAK,CAAC;YAC/E,IAAI,cAAc,CAAC,QAAQ,EAAE,KAAK,MAAM,CAAC,QAAQ,EAAE,EAAE,CAAC;gBACnD,GAAW,CAAC,QAAQ,GAAG,QAAQ,CAAC;gBACjC,OAAO,IAAI,EAAE,CAAC;YAChB,CAAC;YAED,oBAAoB;YACpB,MAAM,UAAU,GAAG,OAAO,CAAC,sBAAsB,CAAC,CAAC;YACnD,MAAM,UAAU,GAAG,MAAM,UAAU,CAAC,OAAO,CAAC;gBAC1C,cAAc,EAAE,cAAc;gBAC9B,MAAM,EAAE,MAAM;gBACd,MAAM,EAAE,QAAQ;aACjB,CAAC,CAAC;YAEH,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,8CAAkB,CAAC,gBAAgB,CAAC;oBAClC,IAAI,EAAE,6CAAiB,CAAC,mBAAmB;oBAC3C,QAAQ,EAAE,4CAAgB,CAAC,IAAI;oBAC/B,OAAO,EAAE,gDAAgD,UAAU,EAAE;oBACrE,EAAE,EAAE,GAAG,CAAC,EAAE,IAAI,SAAS;oBACvB,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;oBAChC,MAAM,EAAE,MAAM;oBACd,IAAI,EAAE,GAAG,CAAC,IAAI;oBACd,MAAM,EAAE,GAAG,CAAC,MAAM;oBAClB,SAAS,EAAE,IAAI,IAAI,EAAE;oBACrB,QAAQ,EAAE,EAAE,UAAU,EAAE,YAAY,EAAE,aAAa,CAAC,SAAS,EAAE;iBAChE,CAAC,CAAC;gBAEH,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,2CAA2C;oBACpD,KAAK,EAAE,oBAAoB;iBAC5B,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,4BAA4B;YAC5B,MAAM,CAAC,aAAa,EAAE,MAAM,CAAC,GAAG,kBAAkB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC9D,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,aAAa,EAAE,MAAM,CAAC,EAAE,CAAC;gBAClD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,uCAAuC,kBAAkB,EAAE;oBACpE,KAAK,EAAE,+BAA+B;iBACvC,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAEA,GAAW,CAAC,QAAQ,GAAG,QAAQ,CAAC;YAChC,GAAW,CAAC,UAAU,GAAG,UAAU,CAAC;YACrC,IAAI,EAAE,CAAC;QACT,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YAC1D,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,iDAAiD;gBAC1D,KAAK,EAAE,0BAA0B;aAClC,CAAC,CAAC;QACL,CAAC;IACH,CAAC,CAAC;AACJ,CAAC,CAAC;AAvFW,QAAA,uBAAuB,2BAuFlC;AAEF;;GAEG;AACI,MAAM,YAAY,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;IACpF,MAAM,IAAI,GAAI,GAAW,CAAC,IAAI,CAAC;IAE/B,IAAI,CAAC,IAAI,EAAE,CAAC;QACV,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,yBAAyB;YAClC,KAAK,EAAE,yBAAyB;SACjC,CAAC,CAAC;QACH,OAAO;IACT,CAAC;IAED,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;QAC1B,8CAAkB,CAAC,gBAAgB,CAAC;YAClC,IAAI,EAAE,6CAAiB,CAAC,oBAAoB;YAC5C,QAAQ,EAAE,4CAAgB,CAAC,IAAI;YAC/B,OAAO,EAAE,mDAAmD;YAC5D,EAAE,EAAE,GAAG,CAAC,EAAE,IAAI,SAAS;YACvB,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;YAChC,MAAM,EAAE,IAAI,CAAC,EAAE;YACf,IAAI,EAAE,GAAG,CAAC,IAAI;YACd,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,QAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,IAAI,EAAE;SAClC,CAAC,CAAC;QAEH,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,2CAA2C;YACpD,KAAK,EAAE,uBAAuB;SAC/B,CAAC,CAAC;QACH,OAAO;IACT,CAAC;IAED,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAnCW,QAAA,YAAY,gBAmCvB;AAEF;;GAEG;AACI,MAAM,oBAAoB,GAAG,CAAC,QAAgB,EAAE,MAAc,EAAE,EAAE;IACvE,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;QAC/D,MAAM,MAAM,GAAI,GAAW,CAAC,MAAM,CAAC;QAEnC,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,6BAA6B;gBACtC,KAAK,EAAE,mBAAmB;aAC3B,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAED,MAAM,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAEjD,IAAI,CAAC,WAAW,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE,CAAC;YACzC,8CAAkB,CAAC,eAAe,CAChC,GAAG,EACH,oCAAoC,QAAQ,IAAI,MAAM,EAAE,EACxD,MAAM,CAAC,KAAK,CACb,CAAC;YAEF,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,2CAA2C,QAAQ,IAAI,MAAM,EAAE;gBACxE,KAAK,EAAE,8BAA8B;aACtC,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AAhCW,QAAA,oBAAoB,wBAgC/B;AAEF,kBAAe;IACb,sBAAsB,EAAtB,8BAAsB;IACtB,uBAAuB,EAAvB,+BAAuB;IACvB,YAAY,EAAZ,oBAAY;IACZ,oBAAoB,EAApB,4BAAoB;CACrB,CAAC"}