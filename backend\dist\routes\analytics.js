"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const auth_1 = require("../middleware/auth");
const usageTracking_1 = require("../middleware/usageTracking");
const analyticsController_1 = require("../controllers/analyticsController");
const router = express_1.default.Router();
// All routes require authentication
router.use(auth_1.authenticate);
/**
 * @route   GET /api/analytics/dashboard
 * @desc    Get dashboard analytics summary
 * @access  Private
 */
router.get('/dashboard', analyticsController_1.getDashboardAnalytics);
/**
 * @route   GET /api/analytics/cash-flow
 * @desc    Get cash flow analytics (basic for all plans, advanced for paid plans)
 * @access  Private
 */
router.get('/cash-flow', analyticsController_1.getCashFlowAnalytics);
/**
 * @route   GET /api/analytics/revenue
 * @desc    Get revenue analytics
 * @access  Private (Professional/Business plans)
 */
router.get('/revenue', (0, usageTracking_1.checkFeatureAccess)('advancedReports'), analyticsController_1.getRevenueAnalytics);
/**
 * @route   GET /api/analytics/customers
 * @desc    Get customer analytics
 * @access  Private (Professional/Business plans)
 */
router.get('/customers', (0, usageTracking_1.checkFeatureAccess)('advancedReports'), analyticsController_1.getCustomerAnalytics);
exports.default = router;
//# sourceMappingURL=analytics.js.map