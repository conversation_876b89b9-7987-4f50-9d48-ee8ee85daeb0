export interface PaymentReminderSettings {
    enabled: boolean;
    reminderDays: number[];
    overdueReminderDays: number[];
    maxReminders: number;
}
export declare class PaymentReminderService {
    private static instance;
    private emailService;
    constructor();
    static getInstance(): PaymentReminderService;
    /**
     * Initialize cron jobs for payment reminders
     */
    private initializeCronJobs;
    /**
     * Process payment reminders for all users
     */
    processPaymentReminders(): Promise<void>;
    /**
     * Check if reminder should be sent and send it
     */
    private checkAndSendReminder;
    /**
     * Send payment reminder email
     */
    private sendPaymentReminder;
    /**
     * Update overdue invoice status
     */
    updateOverdueInvoices(): Promise<void>;
    /**
     * Send manual payment reminder
     */
    sendManualReminder(invoiceId: string, userId: string): Promise<boolean>;
    /**
     * Get payment reminder statistics
     */
    getReminderStats(userId: string): Promise<any>;
}
export default PaymentReminderService;
//# sourceMappingURL=paymentReminderService.d.ts.map