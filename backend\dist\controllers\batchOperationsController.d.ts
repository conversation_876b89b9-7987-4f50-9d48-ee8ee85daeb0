import { Request, Response } from 'express';
export declare const createBatchInvoices: (req: Request, res: Response) => Promise<void>;
export declare const sendBatchEmails: (req: Request, res: Response) => Promise<void>;
export declare const updateBatchInvoiceStatus: (req: Request, res: Response) => Promise<void>;
export declare const deleteBatchInvoices: (req: Request, res: Response) => Promise<void>;
export declare const getBatchOperationHistory: (req: Request, res: Response) => Promise<void>;
//# sourceMappingURL=batchOperationsController.d.ts.map