interface EmailJob {
    id: string;
    type: 'invoice' | 'receipt' | 'reminder' | 'welcome' | 'verification';
    to: string;
    data: any;
    pdfBuffer?: Buffer;
    retries: number;
    maxRetries: number;
    createdAt: Date;
    scheduledAt?: Date;
}
declare class EmailQueueService {
    private static instance;
    private queue;
    private processing;
    private processingInterval;
    static getInstance(): EmailQueueService;
    constructor();
    /**
     * Add email job to queue for background processing
     */
    queueInvoiceEmail(to: string, invoiceData: any, pdfBuffer: Buffer, priority?: boolean): Promise<string>;
    /**
     * Add other email types to queue
     */
    queueEmail(type: EmailJob['type'], to: string, data: any, pdfBuffer?: Buffer): Promise<string>;
    /**
     * Process email queue in background
     */
    private startProcessing;
    /**
     * Process queued emails
     */
    private processQueue;
    /**
     * Get queue status
     */
    getQueueStatus(): {
        queueSize: number;
        processing: boolean;
    };
    /**
     * Clear queue (for testing/emergency)
     */
    clearQueue(): void;
    /**
     * Stop processing
     */
    stop(): void;
    private generateJobId;
}
export default EmailQueueService;
//# sourceMappingURL=emailQueueService.d.ts.map