import { Request, Response } from 'express';
/**
 * Send OTP for login
 */
export declare const sendLoginOTP: (req: Request, res: Response) => Promise<void>;
/**
 * Verify login OTP
 */
export declare const verifyLoginOTP: (req: Request, res: Response) => Promise<void>;
/**
 * Send OTP for password reset
 */
export declare const sendPasswordResetOTP: (req: Request, res: Response) => Promise<void>;
/**
 * Verify password reset OTP
 */
export declare const verifyPasswordResetOTP: (req: Request, res: Response) => Promise<void>;
/**
 * Send OTP for sensitive operations (profile updates, etc.)
 */
export declare const sendOperationOTP: (req: Request, res: Response) => Promise<void>;
/**
 * Verify operation OTP
 */
export declare const verifyOperationOTP: (req: Request, res: Response) => Promise<void>;
/**
 * Get OTP statistics (admin only)
 */
export declare const getOTPStats: (req: Request, res: Response) => Promise<void>;
//# sourceMappingURL=otpController.d.ts.map