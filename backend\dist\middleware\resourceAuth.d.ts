import { Request, Response, NextFunction } from 'express';
/**
 * Middleware to check if user owns the requested resource
 */
export declare const checkResourceOwnership: (resourceModel: any, resourceIdParam?: string) => (req: Request, res: Response, next: NextFunction) => Promise<void>;
/**
 * Middleware to check team-based resource access
 */
export declare const checkTeamResourceAccess: (resourceModel: any, requiredPermission: string) => (req: Request, res: Response, next: NextFunction) => Promise<void>;
/**
 * Middleware for admin-only access
 */
export declare const requireAdmin: (req: Request, res: Response, next: NextFunction) => void;
/**
 * Middleware to check API key permissions for specific actions
 */
export declare const requireApiPermission: (resource: string, action: string) => (req: Request, res: Response, next: NextFunction) => void;
declare const _default: {
    checkResourceOwnership: (resourceModel: any, resourceIdParam?: string) => (req: Request, res: Response, next: NextFunction) => Promise<void>;
    checkTeamResourceAccess: (resourceModel: any, requiredPermission: string) => (req: Request, res: Response, next: NextFunction) => Promise<void>;
    requireAdmin: (req: Request, res: Response, next: NextFunction) => void;
    requireApiPermission: (resource: string, action: string) => (req: Request, res: Response, next: NextFunction) => void;
};
export default _default;
//# sourceMappingURL=resourceAuth.d.ts.map