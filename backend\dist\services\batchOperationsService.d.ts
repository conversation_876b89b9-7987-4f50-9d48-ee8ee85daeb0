export interface BatchInvoiceData {
    templateId?: string;
    customers: Array<{
        name: string;
        email?: string;
        phone?: string;
        address?: string;
        gstNumber?: string;
        stateCode?: string;
    }>;
    items?: Array<{
        description: string;
        quantity: number;
        rate: number;
        unit?: string;
        hsnCode?: string;
        gstRate?: number;
    }>;
    commonData?: {
        invoiceType?: 'gst' | 'non-gst';
        notes?: string;
        terms?: string;
        dueInDays?: number;
    };
}
export interface BatchOperationResult {
    success: boolean;
    totalProcessed: number;
    successCount: number;
    failureCount: number;
    results: Array<{
        success: boolean;
        data?: any;
        error?: string;
        customer: string;
    }>;
}
export declare class BatchOperationsService {
    private static instance;
    private emailService;
    static getInstance(): BatchOperationsService;
    /**
     * Create multiple invoices from a template or common data
     */
    createBatchInvoices(userId: string, batchData: BatchInvoiceData): Promise<BatchOperationResult>;
    /**
     * Create a single invoice for batch processing
     */
    private createSingleInvoice;
    /**
     * Send batch invoice emails
     */
    sendBatchEmails(userId: string, invoiceIds: string[]): Promise<BatchOperationResult>;
    /**
     * Update multiple invoices status
     */
    updateBatchInvoiceStatus(userId: string, invoiceIds: string[], status: string, paymentStatus?: string): Promise<BatchOperationResult>;
    /**
     * Delete multiple invoices
     */
    deleteBatchInvoices(userId: string, invoiceIds: string[]): Promise<BatchOperationResult>;
    /**
     * Send batch invoice email
     */
    private sendBatchInvoiceEmail;
    /**
     * Get user data
     */
    private getUserData;
    /**
     * Generate unique invoice number
     */
    private generateInvoiceNumber;
}
export default BatchOperationsService;
//# sourceMappingURL=batchOperationsService.d.ts.map