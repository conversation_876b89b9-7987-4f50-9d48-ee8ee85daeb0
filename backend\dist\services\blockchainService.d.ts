export declare class BlockchainService {
    private provider;
    private wallet;
    private contract;
    constructor();
    /**
     * Generate a hash for invoice data
     */
    generateInvoiceHash(invoiceData: any): string;
    /**
     * Store invoice hash on blockchain
     */
    storeInvoiceHash(invoiceHash: string, metadata: string): Promise<{
        success: boolean;
        transactionHash?: string;
        blockNumber?: number;
        error?: string;
    }>;
    /**
     * Verify invoice hash on blockchain
     */
    verifyInvoiceHash(invoiceHash: string): Promise<{
        success: boolean;
        exists: boolean;
        timestamp?: number;
        metadata?: string;
        error?: string;
    }>;
    /**
     * Get network information
     */
    getNetworkInfo(): Promise<{
        success: boolean;
        network?: any;
        blockNumber?: number;
        gasPrice?: string;
        error?: string;
    }>;
    /**
     * Get wallet balance
     */
    getWalletBalance(): Promise<{
        success: boolean;
        balance?: string;
        balanceInMatic?: string;
        error?: string;
    }>;
    /**
     * Check if blockchain service is available
     */
    isAvailable(): boolean;
    /**
     * Get service status
     */
    getServiceStatus(): Promise<{
        available: boolean;
        network?: any;
        balance?: string;
        contractAddress?: string;
        error?: string;
    }>;
}
export declare const blockchainService: BlockchainService;
//# sourceMappingURL=blockchainService.d.ts.map