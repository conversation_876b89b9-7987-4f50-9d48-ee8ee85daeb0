"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getTeamStats = exports.checkPermissions = exports.getUserMemberships = exports.acceptInvitation = exports.removeTeamMember = exports.updateTeamMember = exports.getTeamMembers = exports.inviteTeamMember = void 0;
const teamService_1 = __importDefault(require("../services/teamService"));
const TeamMember_1 = __importDefault(require("../models/TeamMember"));
const teamService = new teamService_1.default();
/**
 * Invite a team member
 */
const inviteTeamMember = async (req, res) => {
    try {
        const { email, role } = req.body;
        const organizationId = req.user?._id?.toString(); // The current user's organization
        const invitedBy = req.user?._id?.toString();
        // Validate input
        if (!email || !role) {
            return res.status(400).json({
                success: false,
                message: 'Email and role are required'
            });
        }
        if (!['admin', 'manager', 'user'].includes(role)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid role. Must be admin, manager, or user'
            });
        }
        // Check if current user has permission to invite
        const currentUserMember = await TeamMember_1.default.findOne({
            organizationId,
            userId: invitedBy,
            status: 'active'
        });
        if (!currentUserMember || !currentUserMember.permissions.team.invite) {
            return res.status(403).json({
                success: false,
                message: 'You do not have permission to invite team members'
            });
        }
        const result = await teamService.inviteTeamMember({
            email,
            role,
            organizationId: organizationId,
            invitedBy: invitedBy
        });
        if (result.success) {
            res.status(201).json(result);
        }
        else {
            res.status(400).json(result);
        }
    }
    catch (error) {
        console.error('Error inviting team member:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};
exports.inviteTeamMember = inviteTeamMember;
/**
 * Get team members
 */
const getTeamMembers = async (req, res) => {
    try {
        const organizationId = req.user?._id?.toString();
        const teamMembers = await teamService.getTeamMembers(organizationId);
        res.json({
            success: true,
            data: teamMembers
        });
    }
    catch (error) {
        console.error('Error getting team members:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};
exports.getTeamMembers = getTeamMembers;
/**
 * Update team member
 */
const updateTeamMember = async (req, res) => {
    try {
        const { memberId } = req.params;
        const { role, status, permissions } = req.body;
        const organizationId = req.user?._id?.toString();
        const updatedBy = req.user?._id?.toString();
        const result = await teamService.updateTeamMember(organizationId, memberId, { role, status, permissions }, updatedBy);
        if (result.success) {
            res.json(result);
        }
        else {
            res.status(400).json(result);
        }
    }
    catch (error) {
        console.error('Error updating team member:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};
exports.updateTeamMember = updateTeamMember;
/**
 * Remove team member
 */
const removeTeamMember = async (req, res) => {
    try {
        const { memberId } = req.params;
        const organizationId = req.user?._id?.toString();
        const removedBy = req.user?._id?.toString();
        const result = await teamService.removeTeamMember(organizationId, memberId, removedBy);
        if (result.success) {
            res.json(result);
        }
        else {
            res.status(400).json(result);
        }
    }
    catch (error) {
        console.error('Error removing team member:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};
exports.removeTeamMember = removeTeamMember;
/**
 * Accept team invitation
 */
const acceptInvitation = async (req, res) => {
    try {
        const { token } = req.body;
        const userId = req.user?._id?.toString();
        if (!token) {
            return res.status(400).json({
                success: false,
                message: 'Invitation token is required'
            });
        }
        const result = await teamService.acceptInvitation(token, userId);
        if (result.success) {
            res.json(result);
        }
        else {
            res.status(400).json(result);
        }
    }
    catch (error) {
        console.error('Error accepting invitation:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};
exports.acceptInvitation = acceptInvitation;
/**
 * Get user's team memberships
 */
const getUserMemberships = async (req, res) => {
    try {
        const userId = req.user?._id?.toString();
        const memberships = await teamService.getUserTeamMemberships(userId);
        res.json({
            success: true,
            data: memberships
        });
    }
    catch (error) {
        console.error('Error getting user memberships:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};
exports.getUserMemberships = getUserMemberships;
/**
 * Check user permissions
 */
const checkPermissions = async (req, res) => {
    try {
        const { resource, action } = req.query;
        const userId = req.user?._id?.toString();
        const organizationId = req.headers['x-organization-id'] || req.user?._id?.toString();
        if (!userId) {
            return res.status(401).json({
                success: false,
                message: 'User not authenticated'
            });
        }
        if (!organizationId) {
            return res.status(400).json({
                success: false,
                message: 'Organization ID is required'
            });
        }
        if (!resource || !action) {
            return res.status(400).json({
                success: false,
                message: 'Resource and action are required'
            });
        }
        const hasPermission = await teamService.checkPermission(userId, organizationId, resource, action);
        res.json({
            success: true,
            data: {
                hasPermission,
                resource,
                action
            }
        });
    }
    catch (error) {
        console.error('Error checking permissions:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};
exports.checkPermissions = checkPermissions;
/**
 * Get team statistics
 */
const getTeamStats = async (req, res) => {
    try {
        const organizationId = req.user?._id?.toString();
        const teamMembers = await TeamMember_1.default.find({ organizationId });
        const stats = {
            totalMembers: teamMembers.length,
            activeMembers: teamMembers.filter(m => m.status === 'active').length,
            pendingInvitations: teamMembers.filter(m => m.status === 'pending').length,
            roleDistribution: {
                owner: teamMembers.filter(m => m.role === 'owner').length,
                admin: teamMembers.filter(m => m.role === 'admin').length,
                manager: teamMembers.filter(m => m.role === 'manager').length,
                user: teamMembers.filter(m => m.role === 'user').length
            },
            recentActivity: teamMembers
                .filter(m => m.lastActive)
                .sort((a, b) => new Date(b.lastActive).getTime() - new Date(a.lastActive).getTime())
                .slice(0, 5)
                .map(m => ({
                id: m._id,
                email: m.email,
                role: m.role,
                lastActive: m.lastActive
            }))
        };
        res.json({
            success: true,
            data: stats
        });
    }
    catch (error) {
        console.error('Error getting team stats:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};
exports.getTeamStats = getTeamStats;
//# sourceMappingURL=teamController.js.map