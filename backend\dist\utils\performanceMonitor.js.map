{"version": 3, "file": "performanceMonitor.js", "sourceRoot": "", "sources": ["../../src/utils/performanceMonitor.ts"], "names": [], "mappings": ";AAAA;;GAEG;;;AASH,MAAM,kBAAkB;IAAxB;QAEU,YAAO,GAAwB,EAAE,CAAC;QAClC,eAAU,GAAG,IAAI,CAAC,CAAC,yBAAyB;IAyGtD,CAAC;IAvGC,MAAM,CAAC,WAAW;QAChB,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,CAAC;YACjC,kBAAkB,CAAC,QAAQ,GAAG,IAAI,kBAAkB,EAAE,CAAC;QACzD,CAAC;QACD,OAAO,kBAAkB,CAAC,QAAQ,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,SAAiB;QAC1B,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,OAAO,CAAC,QAAc,EAAE,EAAE;YACxB,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YACxC,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAEjD,sBAAsB;YACtB,IAAI,QAAQ,GAAG,IAAI,EAAE,CAAC,CAAC,YAAY;gBACjC,OAAO,CAAC,IAAI,CAAC,sBAAsB,SAAS,SAAS,QAAQ,IAAI,EAAE,QAAQ,CAAC,CAAC;YAC/E,CAAC;iBAAM,IAAI,QAAQ,GAAG,IAAI,EAAE,CAAC,CAAC,YAAY;gBACxC,OAAO,CAAC,GAAG,CAAC,sBAAsB,SAAS,SAAS,QAAQ,IAAI,CAAC,CAAC;YACpE,CAAC;iBAAM,IAAI,QAAQ,GAAG,IAAI,EAAE,CAAC,CAAC,WAAW;gBACvC,OAAO,CAAC,GAAG,CAAC,MAAM,SAAS,SAAS,QAAQ,IAAI,CAAC,CAAC;YACpD,CAAC;QACH,CAAC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,YAAY,CAAC,SAAiB,EAAE,QAAgB,EAAE,QAAc;QACtE,MAAM,MAAM,GAAsB;YAChC,SAAS;YACT,QAAQ;YACR,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,QAAQ;SACT,CAAC;QAEF,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAE1B,+BAA+B;QAC/B,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;YAC1C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,SAAkB;QACzB,IAAI,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC;QAEnC,IAAI,SAAS,EAAE,CAAC;YACd,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,KAAK,SAAS,CAAC,CAAC;QACxE,CAAC;QAED,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACjC,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;QACtB,CAAC;QAED,MAAM,SAAS,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QACvD,MAAM,GAAG,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QACjD,MAAM,GAAG,GAAG,GAAG,GAAG,SAAS,CAAC,MAAM,CAAC;QACnC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC;QACnC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC;QAEnC,wBAAwB;QACxB,MAAM,MAAM,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAC/C,MAAM,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC;QACpD,MAAM,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC;QACpD,MAAM,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC;QAErD,OAAO;YACL,KAAK,EAAE,eAAe,CAAC,MAAM;YAC7B,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;YACpB,GAAG;YACH,GAAG;YACH,GAAG;YACH,GAAG;YACH,GAAG;YACH,MAAM,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBAC3C,QAAQ,EAAE,CAAC,CAAC,QAAQ;gBACpB,SAAS,EAAE,CAAC,CAAC,SAAS;gBACtB,QAAQ,EAAE,CAAC,CAAC,QAAQ;aACrB,CAAC,CAAC;SACJ,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,aAAa;QACX,MAAM,UAAU,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;QAC/D,OAAO,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,EAAE,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,KAAK;QACH,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;IACpB,CAAC;CACF;AAED,kBAAe,kBAAkB,CAAC;AAElC,6CAA6C;AACtC,MAAM,aAAa,GAAG,CAAC,SAAiB,EAAE,EAAE;IACjD,OAAO,kBAAkB,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;AAChE,CAAC,CAAC;AAFW,QAAA,aAAa,iBAExB"}