"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importStar(require("mongoose"));
const crypto_1 = __importDefault(require("crypto"));
const PaymentTokenSchema = new mongoose_1.Schema({
    tokenId: {
        type: String,
        required: true,
        unique: true,
        index: true
    },
    invoiceId: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'Invoice',
        required: true,
        index: true
    },
    userId: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User',
        required: true,
        index: true
    },
    amount: {
        type: Number,
        required: true,
        min: 0
    },
    currency: {
        type: String,
        default: 'INR',
        uppercase: true
    },
    isUsed: {
        type: Boolean,
        default: false,
        index: true
    },
    usedAt: Date,
    expiresAt: {
        type: Date,
        required: true,
        index: true
    },
    scanCount: {
        type: Number,
        default: 0
    },
    lastScannedAt: Date,
    lastScannedIP: String,
    paymentId: String,
    transactionId: String
}, {
    timestamps: true
});
// Indexes for better query performance
PaymentTokenSchema.index({ invoiceId: 1, isUsed: 1 });
PaymentTokenSchema.index({ expiresAt: 1 }); // For cleanup
PaymentTokenSchema.index({ createdAt: -1 });
// Note: tokenId is now generated manually in the static method generatePaymentToken
// Static method to generate payment token
PaymentTokenSchema.statics.generatePaymentToken = async function (invoiceId, userId, amount, expiryHours = 24) {
    // Check if there's already an active token for this invoice
    const existingToken = await this.findOne({
        invoiceId,
        isUsed: false,
        expiresAt: { $gt: new Date() }
    });
    if (existingToken) {
        return existingToken;
    }
    // Generate unique token ID manually
    const timestamp = Date.now().toString(36);
    const randomBytes = crypto_1.default.randomBytes(16).toString('hex');
    const tokenId = `PT-${timestamp}-${randomBytes}`.toUpperCase();
    // Create new token
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + expiryHours);
    const token = new this({
        tokenId,
        invoiceId,
        userId,
        amount,
        expiresAt
    });
    await token.save();
    return token;
};
// Method to mark token as used
PaymentTokenSchema.methods.markAsUsed = async function (paymentId, transactionId) {
    this.isUsed = true;
    this.usedAt = new Date();
    this.paymentId = paymentId;
    if (transactionId) {
        this.transactionId = transactionId;
    }
    await this.save();
};
// Method to track scan
PaymentTokenSchema.methods.trackScan = async function (ip) {
    this.scanCount += 1;
    this.lastScannedAt = new Date();
    if (ip) {
        this.lastScannedIP = ip;
    }
    await this.save();
};
// Method to check if token is valid
PaymentTokenSchema.methods.isValid = function () {
    return !this.isUsed && new Date() < this.expiresAt;
};
exports.default = mongoose_1.default.model('PaymentToken', PaymentTokenSchema);
//# sourceMappingURL=PaymentToken.js.map