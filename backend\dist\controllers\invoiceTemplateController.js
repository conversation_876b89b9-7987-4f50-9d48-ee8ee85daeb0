"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getTemplateCategories = exports.setDefaultTemplate = exports.createInvoiceFromTemplate = exports.deleteInvoiceTemplate = exports.updateInvoiceTemplate = exports.createInvoiceTemplate = exports.getInvoiceTemplate = exports.getInvoiceTemplates = void 0;
const InvoiceTemplate_1 = __importDefault(require("../models/InvoiceTemplate"));
const Invoice_1 = __importDefault(require("../models/Invoice"));
// Get all templates for a user
const getInvoiceTemplates = async (req, res) => {
    try {
        const userId = req.user?._id;
        if (!userId) {
            res.status(401).json({ success: false, message: 'User not authenticated' });
            return;
        }
        const { category, active } = req.query;
        const filter = { userId };
        if (category)
            filter.category = category;
        if (active !== undefined)
            filter.isActive = active === 'true';
        const templates = await InvoiceTemplate_1.default.find(filter)
            .sort({ isDefault: -1, usageCount: -1, createdAt: -1 });
        res.status(200).json({
            success: true,
            data: templates
        });
    }
    catch (error) {
        console.error('Error fetching invoice templates:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch invoice templates'
        });
    }
};
exports.getInvoiceTemplates = getInvoiceTemplates;
// Get a specific template
const getInvoiceTemplate = async (req, res) => {
    try {
        const userId = req.user?._id;
        const { id } = req.params;
        if (!userId) {
            res.status(401).json({ success: false, message: 'User not authenticated' });
            return;
        }
        const template = await InvoiceTemplate_1.default.findOne({ _id: id, userId: userId.toString() });
        if (!template) {
            res.status(404).json({
                success: false,
                message: 'Invoice template not found'
            });
            return;
        }
        res.status(200).json({
            success: true,
            data: template
        });
    }
    catch (error) {
        console.error('Error fetching invoice template:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch invoice template'
        });
    }
};
exports.getInvoiceTemplate = getInvoiceTemplate;
// Create a new template
const createInvoiceTemplate = async (req, res) => {
    try {
        const userId = req.user?._id;
        if (!userId) {
            res.status(401).json({ success: false, message: 'User not authenticated' });
            return;
        }
        const { templateName, description, invoiceType, customerTemplate, itemsTemplate, defaultSettings, category, tags, isDefault } = req.body;
        // Validate required fields
        if (!templateName || !itemsTemplate?.length) {
            res.status(400).json({
                success: false,
                message: 'Template name and at least one item are required'
            });
            return;
        }
        const template = new InvoiceTemplate_1.default({
            userId: userId.toString(),
            templateName,
            description,
            invoiceType: invoiceType || 'gst',
            customerTemplate,
            itemsTemplate,
            defaultSettings: defaultSettings || {},
            category,
            tags: tags || [],
            isDefault: isDefault || false,
            usageCount: 0
        });
        await template.save();
        res.status(201).json({
            success: true,
            message: 'Invoice template created successfully',
            data: template
        });
    }
    catch (error) {
        console.error('Error creating invoice template:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to create invoice template'
        });
    }
};
exports.createInvoiceTemplate = createInvoiceTemplate;
// Update a template
const updateInvoiceTemplate = async (req, res) => {
    try {
        const userId = req.user?._id;
        const { id } = req.params;
        if (!userId) {
            res.status(401).json({ success: false, message: 'User not authenticated' });
            return;
        }
        const updateData = req.body;
        // Don't allow updating certain fields
        delete updateData.userId;
        delete updateData.usageCount;
        delete updateData.lastUsedAt;
        const template = await InvoiceTemplate_1.default.findOneAndUpdate({ _id: id, userId: userId.toString() }, updateData, { new: true });
        if (!template) {
            res.status(404).json({
                success: false,
                message: 'Invoice template not found'
            });
            return;
        }
        res.status(200).json({
            success: true,
            message: 'Invoice template updated successfully',
            data: template
        });
    }
    catch (error) {
        console.error('Error updating invoice template:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update invoice template'
        });
    }
};
exports.updateInvoiceTemplate = updateInvoiceTemplate;
// Delete a template
const deleteInvoiceTemplate = async (req, res) => {
    try {
        const userId = req.user?._id;
        const { id } = req.params;
        if (!userId) {
            res.status(401).json({ success: false, message: 'User not authenticated' });
            return;
        }
        const result = await InvoiceTemplate_1.default.deleteOne({ _id: id, userId: userId.toString() });
        if (result.deletedCount === 0) {
            res.status(404).json({
                success: false,
                message: 'Invoice template not found'
            });
            return;
        }
        res.status(200).json({
            success: true,
            message: 'Invoice template deleted successfully'
        });
    }
    catch (error) {
        console.error('Error deleting invoice template:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to delete invoice template'
        });
    }
};
exports.deleteInvoiceTemplate = deleteInvoiceTemplate;
// Create invoice from template
const createInvoiceFromTemplate = async (req, res) => {
    try {
        const userId = req.user?._id;
        const { id } = req.params;
        if (!userId) {
            res.status(401).json({ success: false, message: 'User not authenticated' });
            return;
        }
        const template = await InvoiceTemplate_1.default.findOne({ _id: id, userId: userId.toString() });
        if (!template) {
            res.status(404).json({
                success: false,
                message: 'Invoice template not found'
            });
            return;
        }
        // Get any overrides from request body
        const overrides = req.body;
        // Convert template to invoice data
        const invoiceData = template.toInvoiceData(overrides);
        // Generate unique invoice number
        const invoiceNumber = await generateInvoiceNumber(userId.toString());
        // Create the invoice
        const invoice = new Invoice_1.default({
            ...invoiceData,
            invoiceNumber,
            invoiceDate: new Date(),
            userId: userId.toString(),
            status: 'draft',
            paymentStatus: 'pending'
        });
        await invoice.save();
        // Update template usage
        await template.incrementUsage();
        res.status(201).json({
            success: true,
            message: 'Invoice created from template successfully',
            data: invoice
        });
    }
    catch (error) {
        console.error('Error creating invoice from template:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to create invoice from template'
        });
    }
};
exports.createInvoiceFromTemplate = createInvoiceFromTemplate;
// Set template as default
const setDefaultTemplate = async (req, res) => {
    try {
        const userId = req.user?._id;
        const { id } = req.params;
        if (!userId) {
            res.status(401).json({ success: false, message: 'User not authenticated' });
            return;
        }
        const template = await InvoiceTemplate_1.default.findOne({ _id: id, userId: userId.toString() });
        if (!template) {
            res.status(404).json({
                success: false,
                message: 'Invoice template not found'
            });
            return;
        }
        template.isDefault = true;
        await template.save();
        res.status(200).json({
            success: true,
            message: 'Template set as default successfully',
            data: template
        });
    }
    catch (error) {
        console.error('Error setting default template:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to set default template'
        });
    }
};
exports.setDefaultTemplate = setDefaultTemplate;
// Get template categories
const getTemplateCategories = async (req, res) => {
    try {
        const userId = req.user?._id;
        if (!userId) {
            res.status(401).json({ success: false, message: 'User not authenticated' });
            return;
        }
        const categories = await InvoiceTemplate_1.default.distinct('category', { userId: userId.toString(), isActive: true });
        res.status(200).json({
            success: true,
            data: categories.filter(cat => cat) // Remove null/undefined values
        });
    }
    catch (error) {
        console.error('Error fetching template categories:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch template categories'
        });
    }
};
exports.getTemplateCategories = getTemplateCategories;
// Helper function to generate invoice number
async function generateInvoiceNumber(userId) {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    // Find the last invoice number for this user this month
    const lastInvoice = await Invoice_1.default.findOne({
        userId,
        invoiceNumber: new RegExp(`^INV-${year}${month}-`)
    }).sort({ invoiceNumber: -1 });
    let sequence = 1;
    if (lastInvoice) {
        const lastSequence = parseInt(lastInvoice.invoiceNumber.split('-')[2]);
        sequence = lastSequence + 1;
    }
    return `INV-${year}${month}-${String(sequence).padStart(4, '0')}`;
}
//# sourceMappingURL=invoiceTemplateController.js.map