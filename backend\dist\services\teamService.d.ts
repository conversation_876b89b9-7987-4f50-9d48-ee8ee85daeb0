export interface InviteTeamMemberData {
    email: string;
    role: 'admin' | 'manager' | 'user';
    organizationId: string;
    invitedBy: string;
}
export interface TeamMemberUpdate {
    role?: 'admin' | 'manager' | 'user';
    status?: 'active' | 'inactive';
    permissions?: any;
}
export declare class TeamService {
    private emailService;
    constructor();
    /**
     * Invite a new team member
     */
    inviteTeamMember(data: InviteTeamMemberData): Promise<{
        success: boolean;
        message: string;
        teamMember?: any;
    }>;
    /**
     * Accept team invitation
     */
    acceptInvitation(token: string, userId: string): Promise<{
        success: boolean;
        message: string;
    }>;
    /**
     * Get team members for an organization
     */
    getTeamMembers(organizationId: string): Promise<any[]>;
    /**
     * Update team member
     */
    updateTeamMember(organizationId: string, memberId: string, updates: TeamMemberUpdate, updatedBy: string): Promise<{
        success: boolean;
        message: string;
        teamMember?: any;
    }>;
    /**
     * Remove team member
     */
    removeTeamMember(organizationId: string, memberId: string, removedBy: string): Promise<{
        success: boolean;
        message: string;
    }>;
    /**
     * Get user's team memberships
     */
    getUserTeamMemberships(userId: string): Promise<any[]>;
    /**
     * Check if user has permission for a specific action
     */
    checkPermission(userId: string, organizationId: string, resource: string, action: string): Promise<boolean>;
    /**
     * Send invitation email
     */
    private sendInvitationEmail;
}
export default TeamService;
//# sourceMappingURL=teamService.d.ts.map