"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.securityLogger = exports.bruteForceProtection = exports.advancedRateLimiter = exports.speedLimiter = exports.rateLimiters = exports.advancedBruteForceProtection = exports.suspiciousActivityDetection = exports.requestLogger = exports.securityHeaders = void 0;
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const express_slow_down_1 = __importDefault(require("express-slow-down"));
const rate_limiter_flexible_1 = require("rate-limiter-flexible");
const winston_1 = __importDefault(require("winston"));
// Security logger
const securityLogger = winston_1.default.createLogger({
    level: 'info',
    format: winston_1.default.format.combine(winston_1.default.format.timestamp(), winston_1.default.format.errors({ stack: true }), winston_1.default.format.json()),
    defaultMeta: { service: 'invonest-security' },
    transports: [
        new winston_1.default.transports.File({ filename: 'logs/security-error.log', level: 'error' }),
        new winston_1.default.transports.File({ filename: 'logs/security-combined.log' }),
        new winston_1.default.transports.Console({
            format: winston_1.default.format.simple()
        })
    ]
});
exports.securityLogger = securityLogger;
// Rate limiter configurations
const rateLimiters = {
    // General API rate limiting
    general: (0, express_rate_limit_1.default)({
        windowMs: 15 * 60 * 1000, // 15 minutes
        max: 100, // limit each IP to 100 requests per windowMs
        message: {
            success: false,
            message: 'Too many requests from this IP, please try again later.',
            error: 'RATE_LIMIT_EXCEEDED'
        },
        standardHeaders: true,
        legacyHeaders: false,
        handler: (req, res) => {
            securityLogger.warn('Rate limit exceeded', {
                ip: req.ip,
                userAgent: req.get('User-Agent'),
                path: req.path,
                method: req.method
            });
            res.status(429).json({
                success: false,
                message: 'Too many requests from this IP, please try again later.',
                error: 'RATE_LIMIT_EXCEEDED'
            });
        }
    }),
    // Strict rate limiting for authentication endpoints
    auth: (0, express_rate_limit_1.default)({
        windowMs: 15 * 60 * 1000, // 15 minutes
        max: 5, // limit each IP to 5 login attempts per 15 minutes
        message: {
            success: false,
            message: 'Too many authentication attempts, please try again later.',
            error: 'AUTH_RATE_LIMIT_EXCEEDED'
        },
        skipSuccessfulRequests: true,
        handler: (req, res) => {
            securityLogger.error('Authentication rate limit exceeded', {
                ip: req.ip,
                userAgent: req.get('User-Agent'),
                path: req.path,
                email: req.body.email
            });
            res.status(429).json({
                success: false,
                message: 'Too many authentication attempts, please try again later.',
                error: 'AUTH_RATE_LIMIT_EXCEEDED'
            });
        }
    }),
    // OTP rate limiting
    otp: (0, express_rate_limit_1.default)({
        windowMs: 60 * 1000, // 1 minute
        max: 3, // limit each IP to 3 OTP requests per minute
        message: {
            success: false,
            message: 'Too many OTP requests, please wait before requesting again.',
            error: 'OTP_RATE_LIMIT_EXCEEDED'
        },
        handler: (req, res) => {
            securityLogger.warn('OTP rate limit exceeded', {
                ip: req.ip,
                userAgent: req.get('User-Agent'),
                email: req.body.email
            });
            res.status(429).json({
                success: false,
                message: 'Too many OTP requests, please wait before requesting again.',
                error: 'OTP_RATE_LIMIT_EXCEEDED'
            });
        }
    }),
    // API endpoints rate limiting
    api: (0, express_rate_limit_1.default)({
        windowMs: 60 * 1000, // 1 minute
        max: 60, // limit each IP to 60 API requests per minute
        message: {
            success: false,
            message: 'API rate limit exceeded, please slow down.',
            error: 'API_RATE_LIMIT_EXCEEDED'
        }
    })
};
exports.rateLimiters = rateLimiters;
// Slow down middleware for progressive delays
const speedLimiter = (0, express_slow_down_1.default)({
    windowMs: 15 * 60 * 1000, // 15 minutes
    delayAfter: 50, // allow 50 requests per 15 minutes at full speed
    delayMs: () => 500, // slow down subsequent requests by 500ms per request
    maxDelayMs: 20000, // maximum delay of 20 seconds
    validate: { delayMs: false } // Disable the warning
});
exports.speedLimiter = speedLimiter;
// Advanced rate limiter using rate-limiter-flexible
const advancedRateLimiter = new rate_limiter_flexible_1.RateLimiterMemory({
    points: 100, // Number of requests
    duration: 900, // Per 15 minutes
    blockDuration: 900, // Block for 15 minutes if limit exceeded
});
exports.advancedRateLimiter = advancedRateLimiter;
// Brute force protection for login attempts
const bruteForceProtection = new rate_limiter_flexible_1.RateLimiterMemory({
    points: 5, // Number of attempts
    duration: 900, // Per 15 minutes
    blockDuration: 1800, // Block for 30 minutes
});
exports.bruteForceProtection = bruteForceProtection;
// Security headers middleware
const securityHeaders = (req, res, next) => {
    // Additional security headers beyond helmet
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('X-XSS-Protection', '1; mode=block');
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
    res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');
    // Remove server information
    res.removeHeader('X-Powered-By');
    res.removeHeader('Server');
    next();
};
exports.securityHeaders = securityHeaders;
// Request logging middleware
const requestLogger = (req, res, next) => {
    const startTime = Date.now();
    res.on('finish', () => {
        const duration = Date.now() - startTime;
        const logData = {
            method: req.method,
            url: req.url,
            ip: req.ip,
            userAgent: req.get('User-Agent'),
            statusCode: res.statusCode,
            duration,
            userId: req.user?.id,
            timestamp: new Date().toISOString()
        };
        if (res.statusCode >= 400) {
            securityLogger.error('HTTP Error', logData);
        }
        else {
            securityLogger.info('HTTP Request', logData);
        }
    });
    next();
};
exports.requestLogger = requestLogger;
// Suspicious activity detection
const suspiciousActivityDetection = (req, res, next) => {
    const suspiciousPatterns = [
        /(\.\.|\/etc\/|\/proc\/|\/sys\/)/i, // Path traversal
        /(union|select|insert|update|delete|drop|create|alter)/i, // SQL injection
        /(<script|javascript:|vbscript:|onload|onerror)/i, // XSS
        /(eval\(|setTimeout\(|setInterval\()/i, // Code injection
    ];
    const userAgent = req.get('User-Agent') || '';
    const url = req.url;
    const body = JSON.stringify(req.body);
    const isSuspicious = suspiciousPatterns.some(pattern => pattern.test(url) || pattern.test(body) || pattern.test(userAgent));
    if (isSuspicious) {
        securityLogger.error('Suspicious activity detected', {
            ip: req.ip,
            userAgent,
            url,
            body: req.body,
            headers: req.headers,
            timestamp: new Date().toISOString()
        });
        return res.status(400).json({
            success: false,
            message: 'Invalid request detected',
            error: 'SUSPICIOUS_ACTIVITY'
        });
    }
    next();
};
exports.suspiciousActivityDetection = suspiciousActivityDetection;
// Advanced brute force protection middleware
const advancedBruteForceProtection = async (req, res, next) => {
    try {
        const key = `${req.ip}_${req.body.email || 'unknown'}`;
        await bruteForceProtection.consume(key);
        next();
    }
    catch (rejRes) {
        const remainingTime = Math.round((rejRes.msBeforeNext || 60000) / 1000);
        securityLogger.error('Brute force attempt detected', {
            ip: req.ip,
            email: req.body.email,
            userAgent: req.get('User-Agent'),
            remainingTime,
            timestamp: new Date().toISOString()
        });
        res.status(429).json({
            success: false,
            message: `Too many failed attempts. Try again in ${remainingTime} seconds.`,
            error: 'BRUTE_FORCE_PROTECTION',
            retryAfter: remainingTime
        });
    }
};
exports.advancedBruteForceProtection = advancedBruteForceProtection;
//# sourceMappingURL=security.js.map