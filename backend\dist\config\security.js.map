{"version": 3, "file": "security.js", "sourceRoot": "", "sources": ["../../src/config/security.ts"], "names": [], "mappings": ";;;;;;AAAA,oDAA4B;AAmD5B,8CAA8C;AACvC,MAAM,iBAAiB,GAAG,GAAmB,EAAE;IACpD,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,CAAC;IAE3D,OAAO;QACL,GAAG,EAAE;YACH,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,sCAAsC;YACxE,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,IAAI;YACzC,gBAAgB,EAAE,KAAK;YACvB,SAAS,EAAE,OAAO;SACnB;QACD,UAAU,EAAE;YACV,SAAS,EAAE,aAAa;YACxB,SAAS,EAAE,EAAE;YACb,QAAQ,EAAE,EAAE;SACb;QACD,QAAQ,EAAE;YACR,UAAU,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,IAAI,CAAC;YACvD,SAAS,EAAE,CAAC;YACZ,gBAAgB,EAAE,IAAI;YACtB,gBAAgB,EAAE,IAAI;YACtB,cAAc,EAAE,IAAI;YACpB,mBAAmB,EAAE,IAAI;SAC1B;QACD,SAAS,EAAE;YACT,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,aAAa;YACvC,WAAW,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,KAAK,CAAC;YAC1D,eAAe,EAAE,CAAC;YAClB,cAAc,EAAE,CAAC;SAClB;QACD,OAAO,EAAE;YACP,MAAM,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,WAAW;YACxC,MAAM,EAAE,YAAY;YACpB,QAAQ,EAAE,IAAI;YACd,QAAQ,EAAE,YAAY,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK;SAC1C;QACD,IAAI,EAAE;YACJ,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,uBAAuB;YAC3D,WAAW,EAAE,IAAI;YACjB,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC;YAC7D,cAAc,EAAE,CAAC,cAAc,EAAE,eAAe,EAAE,kBAAkB,CAAC;SACtE;QACD,MAAM,EAAE;YACN,WAAW,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,UAAU,CAAC,EAAE,OAAO;YACvE,gBAAgB,EAAE;gBAChB,iBAAiB;gBACjB,YAAY;gBACZ,WAAW;gBACX,WAAW;gBACX,oBAAoB;gBACpB,yEAAyE;gBACzE,0BAA0B;gBAC1B,mEAAmE;aACpE;YACD,QAAQ,EAAE,CAAC;SACZ;QACD,GAAG,EAAE;YACH,cAAc,EAAE,MAAM;YACtB,OAAO,EAAE,KAAK,CAAC,aAAa;SAC7B;KACF,CAAC;AACJ,CAAC,CAAC;AA5DW,QAAA,iBAAiB,qBA4D5B;AAEF,uBAAuB;AACvB,MAAa,iBAAiB;IAM5B;;OAEG;IACH,MAAM,CAAC,WAAW;QAChB,OAAO,gBAAM,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAC5D,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,OAAO,CAAC,IAAY,EAAE,GAAY;QACvC,MAAM,aAAa,GAAG,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,gBAAM,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACzF,MAAM,EAAE,GAAG,gBAAM,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAE7C,MAAM,MAAM,GAAG,gBAAM,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,aAAa,EAAE,EAAE,CAAC,CAAC;QACxE,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;QAE3C,IAAI,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;QACnD,SAAS,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAEjC,MAAM,GAAG,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC;QAEhC,OAAO;YACL,SAAS;YACT,EAAE,EAAE,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC;YACtB,GAAG,EAAE,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC;SACzB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,OAAO,CAAC,aAA6D,EAAE,GAAW;QACvF,MAAM,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAC9C,MAAM,EAAE,GAAG,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;QAChD,MAAM,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAElD,MAAM,QAAQ,GAAG,gBAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,EAAE,aAAa,EAAE,EAAE,CAAC,CAAC;QAC5E,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;QAC7C,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QAEzB,IAAI,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC,SAAS,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QACxE,SAAS,IAAI,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAEpC,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,IAAI,CAAC,IAAY;QACtB,OAAO,gBAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAChE,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,aAAa,CAAC,SAAiB,EAAE;QACtC,OAAO,gBAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,cAAc;QACnB,MAAM,MAAM,GAAG,KAAK,CAAC;QACrB,MAAM,UAAU,GAAG,gBAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;QAChE,OAAO,MAAM,GAAG,UAAU,CAAC;IAC7B,CAAC;;AA1EH,8CA2EC;AA1EyB,2BAAS,GAAG,aAAa,CAAC;AAC1B,2BAAS,GAAG,EAAE,CAAC;AACf,0BAAQ,GAAG,EAAE,CAAC;AACd,2BAAS,GAAG,EAAE,CAAC;AAyEzC,8BAA8B;AAC9B,MAAa,iBAAiB;IAG5B;;OAEG;IACH,MAAM,CAAC,QAAQ,CAAC,QAAgB;QAC9B,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,IAAI,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;YAC5C,MAAM,CAAC,IAAI,CAAC,6BAA6B,IAAI,CAAC,MAAM,CAAC,SAAS,kBAAkB,CAAC,CAAC;QACpF,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,gBAAgB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5D,MAAM,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;QACrE,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,gBAAgB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5D,MAAM,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;QACrE,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YACvD,MAAM,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,mBAAmB,IAAI,CAAC,uCAAuC,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC/F,MAAM,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;QACtE,CAAC;QAED,OAAO;YACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC5B,MAAM;SACP,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,QAAQ,CAAC,SAAiB,EAAE;QACjC,MAAM,SAAS,GAAG,4BAA4B,CAAC;QAC/C,MAAM,SAAS,GAAG,4BAA4B,CAAC;QAC/C,MAAM,OAAO,GAAG,YAAY,CAAC;QAC7B,MAAM,OAAO,GAAG,4BAA4B,CAAC;QAE7C,MAAM,QAAQ,GAAG,SAAS,GAAG,SAAS,GAAG,OAAO,GAAG,OAAO,CAAC;QAC3D,IAAI,QAAQ,GAAG,EAAE,CAAC;QAElB,4DAA4D;QAC5D,QAAQ,IAAI,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;QACpE,QAAQ,IAAI,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;QACpE,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;QAChE,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;QAEhE,yBAAyB;QACzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAChC,QAAQ,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;QACpE,CAAC;QAED,uBAAuB;QACvB,OAAO,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACrE,CAAC;;AA5DH,8CA6DC;AA5DgB,wBAAM,GAAG,IAAA,yBAAiB,GAAE,CAAC,QAAQ,CAAC;AA8DvD,qBAAqB;AACR,QAAA,kBAAkB,GAAG;IAChC,kBAAkB,EAAE,CAAC;IACrB,gBAAgB,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,aAAa;IAC/C,UAAU,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,aAAa;IACzC,eAAe,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,WAAW;IACjD,cAAc,EAAE,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,SAAS;IACpD,oBAAoB,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,UAAU;IAC1D,qBAAqB,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,aAAa;IACpD,yBAAyB,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,WAAW;CACnD,CAAC;AAEX,kBAAe,yBAAiB,CAAC"}