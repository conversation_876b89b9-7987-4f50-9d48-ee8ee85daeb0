import { Request, Response } from 'express';
/**
 * Create a new API key
 */
export declare const createApiKey: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
/**
 * Get user's API keys
 */
export declare const getApiKeys: (req: Request, res: Response) => Promise<void>;
/**
 * Update API key
 */
export declare const updateApiKey: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
/**
 * Delete API key
 */
export declare const deleteApiKey: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
/**
 * Get API key usage statistics
 */
export declare const getUsageStats: (req: Request, res: Response) => Promise<void>;
/**
 * Test API key (for API key holders to verify their key works)
 */
export declare const testApiKey: (req: any, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
/**
 * Get API documentation info
 */
export declare const getApiDocs: (req: Request, res: Response) => Promise<void>;
//# sourceMappingURL=apiKeyController.d.ts.map