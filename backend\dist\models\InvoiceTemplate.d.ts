import mongoose, { Document } from 'mongoose';
export interface IInvoiceTemplate extends Document {
    userId: mongoose.Types.ObjectId;
    templateName: string;
    description?: string;
    invoiceType: 'gst' | 'non-gst';
    customerTemplate?: {
        name?: string;
        email?: string;
        phone?: string;
        address?: string;
        gstNumber?: string;
        stateCode?: string;
    };
    itemsTemplate: Array<{
        description: string;
        quantity?: number;
        rate?: number;
        unit?: string;
        hsnCode?: string;
        gstRate?: number;
    }>;
    defaultSettings: {
        notes?: string;
        terms?: string;
        dueInDays?: number;
    };
    usageCount: number;
    lastUsedAt?: Date;
    category?: string;
    tags?: string[];
    isActive: boolean;
    isDefault: boolean;
    createdAt: Date;
    updatedAt: Date;
    incrementUsage(): Promise<IInvoiceTemplate>;
    toInvoiceData(overrides?: any): any;
}
declare const InvoiceTemplate: mongoose.Model<IInvoiceTemplate, {}, {}, {}, mongoose.Document<unknown, {}, IInvoiceTemplate, {}> & IInvoiceTemplate & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
export default InvoiceTemplate;
//# sourceMappingURL=InvoiceTemplate.d.ts.map