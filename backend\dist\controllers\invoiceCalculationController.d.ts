import { Request, Response } from 'express';
export declare const calculateInvoiceTotals: (req: Request, res: Response) => Promise<void>;
export declare const getGSTRates: (req: Request, res: Response) => Promise<void>;
export declare const validateGSTNumber: (req: Request, res: Response) => Promise<void>;
export declare const getCommonHSNCodes: (req: Request, res: Response) => Promise<void>;
export declare const getIndianStates: (req: Request, res: Response) => Promise<void>;
//# sourceMappingURL=invoiceCalculationController.d.ts.map