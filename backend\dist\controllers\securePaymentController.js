"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.cleanupExpiredTokens = exports.upiCallback = exports.getInvoicePaymentTokens = exports.getPaymentTokenDetails = exports.completeSecurePayment = exports.initiateSecurePayment = exports.validatePaymentToken = exports.generatePaymentLink = void 0;
const securePaymentService_1 = __importDefault(require("../services/securePaymentService"));
const Invoice_1 = __importDefault(require("../models/Invoice"));
// Generate secure payment link
const generatePaymentLink = async (req, res) => {
    try {
        const { invoiceId, expiryHours } = req.body;
        if (!invoiceId) {
            res.status(400).json({
                success: false,
                message: 'Invoice ID is required'
            });
            return;
        }
        // Verify invoice belongs to user
        const invoice = await Invoice_1.default.findOne({
            _id: invoiceId,
            userId: req.user?._id
        });
        if (!invoice) {
            res.status(404).json({
                success: false,
                message: 'Invoice not found'
            });
            return;
        }
        const result = await securePaymentService_1.default.generateSecurePaymentLink({
            invoiceId,
            amount: invoice.grandTotal,
            expiryHours: expiryHours || 24
        });
        res.status(201).json({
            success: true,
            message: 'Secure payment link generated',
            data: {
                tokenId: result.token.tokenId,
                paymentUrl: result.paymentUrl,
                qrCodeDataUrl: result.qrCodeDataUrl,
                expiresAt: result.token.expiresAt,
                amount: result.token.amount
            }
        });
    }
    catch (error) {
        console.error('Generate payment link error:', error);
        res.status(500).json({
            success: false,
            message: 'Error generating payment link'
        });
    }
};
exports.generatePaymentLink = generatePaymentLink;
// Validate payment token (public endpoint)
const validatePaymentToken = async (req, res) => {
    try {
        const { tokenId } = req.params;
        const clientIP = req.ip || req.connection.remoteAddress;
        const validation = await securePaymentService_1.default.validatePaymentToken(tokenId, clientIP);
        if (!validation.isValid) {
            res.status(400).json({
                success: false,
                message: validation.error || 'Invalid payment token'
            });
            return;
        }
        res.status(200).json({
            success: true,
            message: 'Payment token is valid',
            data: {
                tokenId: validation.token?.tokenId,
                amount: validation.token?.amount,
                currency: validation.token?.currency,
                expiresAt: validation.token?.expiresAt,
                invoice: {
                    invoiceNumber: validation.invoice?.invoiceNumber,
                    invoiceDate: validation.invoice?.invoiceDate,
                    customer: validation.invoice?.customer
                },
                seller: {
                    name: validation.seller?.name,
                    businessName: validation.seller?.businessName,
                    upiId: validation.seller?.upiId
                }
            }
        });
    }
    catch (error) {
        console.error('Validate payment token error:', error);
        res.status(500).json({
            success: false,
            message: 'Error validating payment token'
        });
    }
};
exports.validatePaymentToken = validatePaymentToken;
// Initiate payment using secure token (public endpoint)
const initiateSecurePayment = async (req, res) => {
    try {
        const { tokenId } = req.params;
        const { paymentMethod, customerInfo } = req.body;
        const clientIP = req.ip || req.connection.remoteAddress;
        if (!paymentMethod) {
            res.status(400).json({
                success: false,
                message: 'Payment method is required'
            });
            return;
        }
        const result = await securePaymentService_1.default.initiatePayment({
            tokenId,
            paymentMethod,
            customerInfo,
            ip: clientIP
        });
        if (!result.success) {
            res.status(400).json({
                success: false,
                message: result.error || 'Error initiating payment'
            });
            return;
        }
        res.status(200).json({
            success: true,
            message: 'Payment initiated successfully',
            data: {
                paymentId: result.paymentId
            }
        });
    }
    catch (error) {
        console.error('Initiate secure payment error:', error);
        res.status(500).json({
            success: false,
            message: 'Error initiating payment'
        });
    }
};
exports.initiateSecurePayment = initiateSecurePayment;
// Complete payment using secure token (public endpoint)
const completeSecurePayment = async (req, res) => {
    try {
        const { tokenId } = req.params;
        const { transactionId, gatewayResponse, customerInfo } = req.body;
        if (!transactionId) {
            res.status(400).json({
                success: false,
                message: 'Transaction ID is required'
            });
            return;
        }
        const result = await securePaymentService_1.default.completePayment(tokenId, transactionId, {
            gatewayResponse,
            customerInfo
        });
        if (!result.success) {
            res.status(400).json({
                success: false,
                message: result.error || 'Error completing payment'
            });
            return;
        }
        res.status(200).json({
            success: true,
            message: 'Payment completed successfully',
            data: {
                paymentId: result.paymentId,
                receiptNumber: result.receiptNumber
            }
        });
    }
    catch (error) {
        console.error('Complete secure payment error:', error);
        res.status(500).json({
            success: false,
            message: 'Error completing payment'
        });
    }
};
exports.completeSecurePayment = completeSecurePayment;
// Get payment token details (authenticated)
const getPaymentTokenDetails = async (req, res) => {
    try {
        const { tokenId } = req.params;
        const token = await securePaymentService_1.default.getPaymentTokenDetails(tokenId);
        if (!token) {
            res.status(404).json({
                success: false,
                message: 'Payment token not found'
            });
            return;
        }
        // Check if user owns this token
        if (token.userId.toString() !== req.user?._id.toString()) {
            res.status(403).json({
                success: false,
                message: 'Access denied'
            });
            return;
        }
        res.status(200).json({
            success: true,
            message: 'Payment token details retrieved',
            data: token
        });
    }
    catch (error) {
        console.error('Get payment token details error:', error);
        res.status(500).json({
            success: false,
            message: 'Error retrieving payment token details'
        });
    }
};
exports.getPaymentTokenDetails = getPaymentTokenDetails;
// Get invoice payment tokens (authenticated)
const getInvoicePaymentTokens = async (req, res) => {
    try {
        const { invoiceId } = req.params;
        // Verify invoice belongs to user
        const invoice = await Invoice_1.default.findOne({
            _id: invoiceId,
            userId: req.user?._id
        });
        if (!invoice) {
            res.status(404).json({
                success: false,
                message: 'Invoice not found'
            });
            return;
        }
        const tokens = await securePaymentService_1.default.getInvoicePaymentTokens(invoiceId);
        res.status(200).json({
            success: true,
            message: 'Invoice payment tokens retrieved',
            data: tokens
        });
    }
    catch (error) {
        console.error('Get invoice payment tokens error:', error);
        res.status(500).json({
            success: false,
            message: 'Error retrieving invoice payment tokens'
        });
    }
};
exports.getInvoicePaymentTokens = getInvoicePaymentTokens;
// UPI callback handler (webhook)
const upiCallback = async (req, res) => {
    try {
        const { tokenId } = req.params;
        const { transactionId, status, amount } = req.body;
        console.log(`📱 UPI callback received for token: ${tokenId}`, { transactionId, status, amount });
        if (status === 'SUCCESS' && transactionId) {
            const result = await securePaymentService_1.default.completePayment(tokenId, transactionId, {
                gatewayResponse: req.body
            });
            if (result.success) {
                console.log(`✅ UPI payment completed: ${tokenId} -> ${result.paymentId}`);
            }
            else {
                console.error(`❌ UPI payment completion failed: ${result.error}`);
            }
        }
        res.status(200).json({
            success: true,
            message: 'UPI callback processed'
        });
    }
    catch (error) {
        console.error('UPI callback error:', error);
        res.status(500).json({
            success: false,
            message: 'UPI callback processing failed'
        });
    }
};
exports.upiCallback = upiCallback;
// Cleanup expired tokens (admin)
const cleanupExpiredTokens = async (req, res) => {
    try {
        const deletedCount = await securePaymentService_1.default.cleanupExpiredTokens();
        res.status(200).json({
            success: true,
            message: 'Expired tokens cleaned up',
            data: {
                deletedCount
            }
        });
    }
    catch (error) {
        console.error('Cleanup expired tokens error:', error);
        res.status(500).json({
            success: false,
            message: 'Error cleaning up expired tokens'
        });
    }
};
exports.cleanupExpiredTokens = cleanupExpiredTokens;
//# sourceMappingURL=securePaymentController.js.map