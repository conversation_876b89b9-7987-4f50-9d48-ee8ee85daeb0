{"version": 3, "file": "numberToWords.js", "sourceRoot": "", "sources": ["../../src/utils/numberToWords.ts"], "names": [], "mappings": ";AAAA;;GAEG;;AAuEH,sCAiBC;AAED,oDAOC;AA/FD,MAAM,IAAI,GAAG;IACX,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM;IAC1E,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS;IACvE,WAAW,EAAE,UAAU,EAAE,UAAU;CACpC,CAAC;AAEF,MAAM,IAAI,GAAG;IACX,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ;CACrF,CAAC;AAEF,MAAM,MAAM,GAAG;IACb,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO;CAChC,CAAC;AAEF,SAAS,eAAe,CAAC,GAAW;IAClC,IAAI,MAAM,GAAG,EAAE,CAAC;IAEhB,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC;QACf,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,WAAW,CAAC;QACpD,GAAG,IAAI,GAAG,CAAC;IACb,CAAC;IAED,IAAI,GAAG,IAAI,EAAE,EAAE,CAAC;QACd,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC;QAC3C,GAAG,IAAI,EAAE,CAAC;IACZ,CAAC;IAED,IAAI,GAAG,GAAG,CAAC,EAAE,CAAC;QACZ,MAAM,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;IAC5B,CAAC;IAED,OAAO,MAAM,CAAC,IAAI,EAAE,CAAC;AACvB,CAAC;AAED,SAAS,cAAc,CAAC,GAAW;IACjC,IAAI,GAAG,KAAK,CAAC;QAAE,OAAO,MAAM,CAAC;IAE7B,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,IAAI,UAAU,GAAG,CAAC,CAAC;IAEnB,gBAAgB;IAChB,IAAI,GAAG,IAAI,QAAQ,EAAE,CAAC;QACpB,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,QAAQ,CAAC,CAAC;QAC1C,MAAM,IAAI,eAAe,CAAC,MAAM,CAAC,GAAG,SAAS,CAAC;QAC9C,GAAG,IAAI,QAAQ,CAAC;IAClB,CAAC;IAED,eAAe;IACf,IAAI,GAAG,IAAI,MAAM,EAAE,CAAC;QAClB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,MAAM,CAAC,CAAC;QACvC,MAAM,IAAI,eAAe,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAC;QAC5C,GAAG,IAAI,MAAM,CAAC;IAChB,CAAC;IAED,mBAAmB;IACnB,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;QAChB,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC;QACzC,MAAM,IAAI,eAAe,CAAC,SAAS,CAAC,GAAG,YAAY,CAAC;QACpD,GAAG,IAAI,IAAI,CAAC;IACd,CAAC;IAED,kCAAkC;IAClC,IAAI,GAAG,GAAG,CAAC,EAAE,CAAC;QACZ,MAAM,IAAI,eAAe,CAAC,GAAG,CAAC,CAAC;IACjC,CAAC;IAED,OAAO,MAAM,CAAC,IAAI,EAAE,CAAC;AACvB,CAAC;AAED,SAAgB,aAAa,CAAC,MAAc,EAAE,WAAmB,QAAQ;IACvE,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IACvC,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC;IAE7D,IAAI,MAAM,GAAG,cAAc,CAAC,WAAW,CAAC,CAAC;IAEzC,IAAI,MAAM,EAAE,CAAC;QACX,MAAM,IAAI,IAAI,QAAQ,EAAE,CAAC;IAC3B,CAAC;IAED,IAAI,WAAW,GAAG,CAAC,EAAE,CAAC;QACpB,MAAM,IAAI,QAAQ,cAAc,CAAC,WAAW,CAAC,QAAQ,CAAC;IACxD,CAAC;IAED,MAAM,IAAI,OAAO,CAAC;IAElB,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAgB,oBAAoB,CAAC,MAAc;IACjD,OAAO,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;QACpC,KAAK,EAAE,UAAU;QACjB,QAAQ,EAAE,KAAK;QACf,qBAAqB,EAAE,CAAC;QACxB,qBAAqB,EAAE,CAAC;KACzB,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AACpB,CAAC"}