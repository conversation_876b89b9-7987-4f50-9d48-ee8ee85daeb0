"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importStar(require("mongoose"));
const RecurringInvoiceSchema = new mongoose_1.Schema({
    userId: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    templateName: {
        type: String,
        required: true,
        trim: true,
        maxlength: 100
    },
    frequency: {
        type: String,
        enum: ['weekly', 'monthly', 'quarterly', 'yearly'],
        required: true
    },
    interval: {
        type: Number,
        required: true,
        min: 1,
        max: 12,
        default: 1
    },
    startDate: {
        type: Date,
        required: true
    },
    endDate: {
        type: Date
    },
    nextGenerationDate: {
        type: Date,
        required: true
    },
    invoiceTemplate: {
        invoiceType: {
            type: String,
            enum: ['gst', 'non-gst'],
            required: true,
            default: 'gst'
        },
        customer: {
            name: { type: String, required: true, trim: true },
            email: { type: String, trim: true, lowercase: true },
            phone: { type: String, trim: true },
            address: { type: String, trim: true },
            gstNumber: { type: String, trim: true, uppercase: true },
            stateCode: { type: String, trim: true }
        },
        items: [{
                description: { type: String, required: true, trim: true },
                quantity: { type: Number, required: true, min: 0.01 },
                rate: { type: Number, required: true, min: 0 },
                unit: { type: String, trim: true, default: 'pcs' },
                hsnCode: { type: String, trim: true },
                gstRate: { type: Number, min: 0, max: 28 }
            }],
        notes: { type: String, trim: true },
        terms: { type: String, trim: true },
        dueInDays: { type: Number, min: 0, default: 30 }
    },
    isActive: {
        type: Boolean,
        default: true
    },
    lastGeneratedDate: {
        type: Date
    },
    totalGenerated: {
        type: Number,
        default: 0,
        min: 0
    },
    maxGenerations: {
        type: Number,
        min: 1
    },
    generatedInvoices: [{
            type: mongoose_1.Schema.Types.ObjectId,
            ref: 'Invoice'
        }]
}, {
    timestamps: true
});
// Indexes for better query performance
RecurringInvoiceSchema.index({ userId: 1, isActive: 1 });
RecurringInvoiceSchema.index({ nextGenerationDate: 1, isActive: 1 });
RecurringInvoiceSchema.index({ userId: 1, templateName: 1 });
// Instance methods
RecurringInvoiceSchema.methods.calculateNextGenerationDate = function () {
    const current = this.nextGenerationDate || this.startDate;
    const next = new Date(current);
    switch (this.frequency) {
        case 'weekly':
            next.setDate(next.getDate() + (7 * this.interval));
            break;
        case 'monthly':
            next.setMonth(next.getMonth() + this.interval);
            break;
        case 'quarterly':
            next.setMonth(next.getMonth() + (3 * this.interval));
            break;
        case 'yearly':
            next.setFullYear(next.getFullYear() + this.interval);
            break;
    }
    return next;
};
RecurringInvoiceSchema.methods.shouldGenerate = function () {
    if (!this.isActive)
        return false;
    if (this.maxGenerations && this.totalGenerated >= this.maxGenerations)
        return false;
    if (this.endDate && new Date() > this.endDate)
        return false;
    return new Date() >= this.nextGenerationDate;
};
const RecurringInvoice = mongoose_1.default.model('RecurringInvoice', RecurringInvoiceSchema);
exports.default = RecurringInvoice;
//# sourceMappingURL=RecurringInvoice.js.map