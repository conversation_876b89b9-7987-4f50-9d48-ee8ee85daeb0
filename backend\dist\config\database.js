"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importDefault(require("mongoose"));
const complianceSeeder_1 = require("../services/complianceSeeder");
const connectDB = async () => {
    try {
        const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/invonest';
        await mongoose_1.default.connect(mongoURI);
        console.log('✅ MongoDB connected successfully');
        console.log(`📍 Database: ${mongoose_1.default.connection.name}`);
        // Seed compliance deadlines
        await (0, complianceSeeder_1.seedComplianceDeadlines)();
    }
    catch (error) {
        console.error('❌ MongoDB connection error:', error);
        process.exit(1);
    }
};
// Handle connection events
mongoose_1.default.connection.on('disconnected', () => {
    console.log('⚠️  MongoDB disconnected');
});
mongoose_1.default.connection.on('error', (err) => {
    console.error('❌ MongoDB error:', err);
});
process.on('SIGINT', async () => {
    await mongoose_1.default.connection.close();
    console.log('🔒 MongoDB connection closed through app termination');
    process.exit(0);
});
exports.default = connectDB;
//# sourceMappingURL=database.js.map