export declare function seedComplianceDeadlines(): Promise<import("mongoose").MergeType<import("mongoose").Document<unknown, {}, import("../models/Compliance").IComplianceDeadline, {}> & import("../models/Compliance").IComplianceDeadline & Required<{
    _id: unknown;
}> & {
    __v: number;
}, Omit<{
    title: string;
    description: string;
    type: string;
    category: string;
    dueDate: Date;
    frequency: string;
    applicableFor: string[];
    priority: string;
    penaltyInfo: {
        lateFilingPenalty: string;
        interestRate: string;
        additionalCharges: string;
    };
    resources: {
        officialLink: string;
        formNumber: string;
    };
}, "_id">>[] | undefined>;
export declare function updateComplianceDeadlines(): Promise<void>;
//# sourceMappingURL=complianceSeeder.d.ts.map