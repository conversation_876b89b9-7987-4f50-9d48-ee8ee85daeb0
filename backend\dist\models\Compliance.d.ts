import mongoose, { Document } from 'mongoose';
export interface IComplianceDeadline extends Document {
    title: string;
    description: string;
    type: 'gst' | 'tds' | 'income_tax' | 'pf' | 'esi' | 'custom';
    category: 'filing' | 'payment' | 'return' | 'audit' | 'other';
    dueDate: Date;
    frequency: 'monthly' | 'quarterly' | 'annually' | 'one_time';
    applicableFor: string[];
    priority: 'low' | 'medium' | 'high' | 'critical';
    nextDueDate?: Date;
    lastUpdated: Date;
    penaltyInfo?: {
        lateFilingPenalty?: string;
        interestRate?: string;
        additionalCharges?: string;
    };
    resources?: {
        officialLink?: string;
        guideLink?: string;
        formNumber?: string;
    };
    isActive: boolean;
    createdAt: Date;
    updatedAt: Date;
}
export interface IUserCompliance extends Document {
    userId: mongoose.Types.ObjectId;
    complianceId: mongoose.Types.ObjectId;
    isEnabled: boolean;
    customDueDate?: Date;
    reminderDays: number[];
    isCompleted: boolean;
    completedDate?: Date;
    notes?: string;
    nextDueDate: Date;
    createdAt: Date;
    updatedAt: Date;
}
export declare const ComplianceDeadline: mongoose.Model<IComplianceDeadline, {}, {}, {}, mongoose.Document<unknown, {}, IComplianceDeadline, {}> & IComplianceDeadline & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
export declare const UserCompliance: mongoose.Model<IUserCompliance, {}, {}, {}, mongoose.Document<unknown, {}, IUserCompliance, {}> & IUserCompliance & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
//# sourceMappingURL=Compliance.d.ts.map