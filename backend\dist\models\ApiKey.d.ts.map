{"version": 3, "file": "ApiKey.d.ts", "sourceRoot": "", "sources": ["../../src/models/ApiKey.ts"], "names": [], "mappings": "AAAA,OAAO,QAAQ,EAAE,EAAE,QAAQ,EAAU,MAAM,UAAU,CAAC;AAGtD,MAAM,WAAW,OAAQ,SAAQ,QAAQ;IACvC,MAAM,EAAE,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC;IAChC,IAAI,EAAE,MAAM,CAAC;IACb,KAAK,EAAE,MAAM,CAAC;IACd,SAAS,EAAE,MAAM,CAAC;IAClB,WAAW,EAAE;QACX,QAAQ,EAAE;YACR,IAAI,EAAE,OAAO,CAAC;YACd,MAAM,EAAE,OAAO,CAAC;YAChB,MAAM,EAAE,OAAO,CAAC;YAChB,MAAM,EAAE,OAAO,CAAC;SACjB,CAAC;QACF,SAAS,EAAE;YACT,IAAI,EAAE,OAAO,CAAC;YACd,MAAM,EAAE,OAAO,CAAC;YAChB,MAAM,EAAE,OAAO,CAAC;YAChB,MAAM,EAAE,OAAO,CAAC;SACjB,CAAC;QACF,SAAS,EAAE;YACT,IAAI,EAAE,OAAO,CAAC;YACd,MAAM,EAAE,OAAO,CAAC;SACjB,CAAC;KACH,CAAC;IACF,QAAQ,EAAE,OAAO,CAAC;IAClB,QAAQ,CAAC,EAAE,IAAI,CAAC;IAChB,UAAU,EAAE,MAAM,CAAC;IACnB,SAAS,EAAE;QACT,iBAAiB,EAAE,MAAM,CAAC;QAC1B,eAAe,EAAE,MAAM,CAAC;QACxB,cAAc,EAAE,MAAM,CAAC;KACxB,CAAC;IACF,SAAS,CAAC,EAAE,IAAI,CAAC;IACjB,SAAS,EAAE,IAAI,CAAC;IAChB,SAAS,EAAE,IAAI,CAAC;IAGhB,WAAW,IAAI;QAAE,KAAK,EAAE,MAAM,CAAC;QAAC,MAAM,EAAE,MAAM,CAAA;KAAE,CAAC;IACjD,SAAS,CAAC,WAAW,EAAE,MAAM,GAAG,OAAO,CAAC;CACzC;;;;;;AA2HD,wBAA+D"}