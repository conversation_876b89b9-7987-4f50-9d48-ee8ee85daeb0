"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.securityMonitoring = exports.SecuritySeverity = exports.SecurityEventType = void 0;
const winston_1 = __importDefault(require("winston"));
const winston_daily_rotate_file_1 = __importDefault(require("winston-daily-rotate-file"));
// Security event types
var SecurityEventType;
(function (SecurityEventType) {
    SecurityEventType["LOGIN_SUCCESS"] = "LOGIN_SUCCESS";
    SecurityEventType["LOGIN_FAILURE"] = "LOGIN_FAILURE";
    SecurityEventType["BRUTE_FORCE_ATTEMPT"] = "BRUTE_FORCE_ATTEMPT";
    SecurityEventType["SUSPICIOUS_ACTIVITY"] = "SUSPICIOUS_ACTIVITY";
    SecurityEventType["RATE_LIMIT_EXCEEDED"] = "RATE_LIMIT_EXCEEDED";
    SecurityEventType["UNAUTHORIZED_ACCESS"] = "UNAUTHORIZED_ACCESS";
    SecurityEventType["DATA_BREACH_ATTEMPT"] = "DATA_BREACH_ATTEMPT";
    SecurityEventType["API_KEY_MISUSE"] = "API_KEY_MISUSE";
    SecurityEventType["PRIVILEGE_ESCALATION"] = "PRIVILEGE_ESCALATION";
    SecurityEventType["ACCOUNT_LOCKOUT"] = "ACCOUNT_LOCKOUT";
    SecurityEventType["PASSWORD_RESET"] = "PASSWORD_RESET";
    SecurityEventType["EMAIL_VERIFICATION"] = "EMAIL_VERIFICATION";
    SecurityEventType["OTP_FAILURE"] = "OTP_FAILURE";
    SecurityEventType["FILE_UPLOAD_VIOLATION"] = "FILE_UPLOAD_VIOLATION";
    SecurityEventType["SQL_INJECTION_ATTEMPT"] = "SQL_INJECTION_ATTEMPT";
    SecurityEventType["XSS_ATTEMPT"] = "XSS_ATTEMPT";
})(SecurityEventType || (exports.SecurityEventType = SecurityEventType = {}));
var SecuritySeverity;
(function (SecuritySeverity) {
    SecuritySeverity["LOW"] = "LOW";
    SecuritySeverity["MEDIUM"] = "MEDIUM";
    SecuritySeverity["HIGH"] = "HIGH";
    SecuritySeverity["CRITICAL"] = "CRITICAL";
})(SecuritySeverity || (exports.SecuritySeverity = SecuritySeverity = {}));
class SecurityMonitoringService {
    constructor() {
        this.alertThresholds = new Map();
        this.eventCounts = new Map();
        this.initializeLogger();
        this.initializeAlertThresholds();
    }
    initializeLogger() {
        this.logger = winston_1.default.createLogger({
            level: 'info',
            format: winston_1.default.format.combine(winston_1.default.format.timestamp(), winston_1.default.format.errors({ stack: true }), winston_1.default.format.json()),
            defaultMeta: { service: 'invonest-security-monitoring' },
            transports: [
                // Daily rotating file for security events
                new winston_daily_rotate_file_1.default({
                    filename: 'logs/security-%DATE%.log',
                    datePattern: 'YYYY-MM-DD',
                    zippedArchive: true,
                    maxSize: '20m',
                    maxFiles: '30d'
                }),
                // Separate file for critical security events
                new winston_daily_rotate_file_1.default({
                    filename: 'logs/security-critical-%DATE%.log',
                    datePattern: 'YYYY-MM-DD',
                    level: 'error',
                    zippedArchive: true,
                    maxSize: '20m',
                    maxFiles: '90d'
                }),
                // Console output for development
                new winston_1.default.transports.Console({
                    format: winston_1.default.format.combine(winston_1.default.format.colorize(), winston_1.default.format.simple())
                })
            ]
        });
    }
    initializeAlertThresholds() {
        // Set alert thresholds for different event types
        this.alertThresholds.set(SecurityEventType.LOGIN_FAILURE, 5);
        this.alertThresholds.set(SecurityEventType.BRUTE_FORCE_ATTEMPT, 3);
        this.alertThresholds.set(SecurityEventType.SUSPICIOUS_ACTIVITY, 1);
        this.alertThresholds.set(SecurityEventType.RATE_LIMIT_EXCEEDED, 10);
        this.alertThresholds.set(SecurityEventType.UNAUTHORIZED_ACCESS, 3);
        this.alertThresholds.set(SecurityEventType.DATA_BREACH_ATTEMPT, 1);
        this.alertThresholds.set(SecurityEventType.API_KEY_MISUSE, 5);
        this.alertThresholds.set(SecurityEventType.SQL_INJECTION_ATTEMPT, 1);
        this.alertThresholds.set(SecurityEventType.XSS_ATTEMPT, 1);
    }
    /**
     * Log a security event
     */
    logSecurityEvent(event) {
        const logLevel = this.getLogLevel(event.severity);
        this.logger.log(logLevel, event.message, {
            type: event.type,
            severity: event.severity,
            ip: event.ip,
            userAgent: event.userAgent,
            userId: event.userId,
            email: event.email,
            path: event.path,
            method: event.method,
            payload: event.payload,
            timestamp: event.timestamp,
            metadata: event.metadata
        });
        // Check if this event type should trigger an alert
        this.checkForAlerts(event);
    }
    /**
     * Log authentication events
     */
    logAuthEvent(type, req, user, additionalInfo) {
        const event = {
            type,
            severity: type === SecurityEventType.LOGIN_FAILURE ? SecuritySeverity.MEDIUM : SecuritySeverity.LOW,
            message: type === SecurityEventType.LOGIN_SUCCESS ? 'User login successful' : 'User login failed',
            ip: req.ip || 'unknown',
            userAgent: req.get('User-Agent'),
            userId: user?._id?.toString(),
            email: req.body.email || user?.email,
            path: req.path,
            method: req.method,
            timestamp: new Date(),
            metadata: additionalInfo
        };
        this.logSecurityEvent(event);
    }
    /**
     * Log suspicious activity
     */
    logSuspiciousActivity(req, reason, severity = SecuritySeverity.HIGH, additionalData) {
        const event = {
            type: SecurityEventType.SUSPICIOUS_ACTIVITY,
            severity,
            message: `Suspicious activity detected: ${reason}`,
            ip: req.ip || 'unknown',
            userAgent: req.get('User-Agent'),
            userId: req.user?.id,
            path: req.path,
            method: req.method,
            payload: req.body,
            timestamp: new Date(),
            metadata: additionalData
        };
        this.logSecurityEvent(event);
    }
    /**
     * Log rate limiting events
     */
    logRateLimitEvent(req, limitType) {
        const event = {
            type: SecurityEventType.RATE_LIMIT_EXCEEDED,
            severity: SecuritySeverity.MEDIUM,
            message: `Rate limit exceeded for ${limitType}`,
            ip: req.ip || 'unknown',
            userAgent: req.get('User-Agent'),
            userId: req.user?.id,
            path: req.path,
            method: req.method,
            timestamp: new Date(),
            metadata: { limitType }
        };
        this.logSecurityEvent(event);
    }
    /**
     * Log API key misuse
     */
    logApiKeyMisuse(req, reason, keyId) {
        const event = {
            type: SecurityEventType.API_KEY_MISUSE,
            severity: SecuritySeverity.HIGH,
            message: `API key misuse detected: ${reason}`,
            ip: req.ip || 'unknown',
            userAgent: req.get('User-Agent'),
            path: req.path,
            method: req.method,
            timestamp: new Date(),
            metadata: { keyId, reason }
        };
        this.logSecurityEvent(event);
    }
    /**
     * Log potential injection attacks
     */
    logInjectionAttempt(req, type, detectedPattern) {
        const event = {
            type,
            severity: SecuritySeverity.CRITICAL,
            message: `${type} detected`,
            ip: req.ip || 'unknown',
            userAgent: req.get('User-Agent'),
            userId: req.user?.id,
            path: req.path,
            method: req.method,
            payload: req.body,
            timestamp: new Date(),
            metadata: { detectedPattern }
        };
        this.logSecurityEvent(event);
    }
    /**
     * Get security statistics
     */
    getSecurityStats(timeframe = 'day') {
        // This would typically query a database or log aggregation service
        // For now, return a placeholder structure
        return Promise.resolve({
            timeframe,
            totalEvents: this.eventCounts.size,
            eventsByType: Object.fromEntries(this.eventCounts),
            criticalEvents: 0,
            highSeverityEvents: 0,
            topIPs: [],
            topUserAgents: [],
            timestamp: new Date()
        });
    }
    getLogLevel(severity) {
        switch (severity) {
            case SecuritySeverity.LOW:
                return 'info';
            case SecuritySeverity.MEDIUM:
                return 'warn';
            case SecuritySeverity.HIGH:
            case SecuritySeverity.CRITICAL:
                return 'error';
            default:
                return 'info';
        }
    }
    checkForAlerts(event) {
        const threshold = this.alertThresholds.get(event.type);
        if (!threshold)
            return;
        const key = `${event.type}_${event.ip}_${new Date().getHours()}`;
        const currentCount = this.eventCounts.get(key) || 0;
        this.eventCounts.set(key, currentCount + 1);
        if (currentCount + 1 >= threshold) {
            this.triggerAlert(event, currentCount + 1);
        }
    }
    triggerAlert(event, count) {
        const alertMessage = `SECURITY ALERT: ${event.type} threshold exceeded (${count} events) from IP ${event.ip}`;
        this.logger.error(alertMessage, {
            alertType: 'THRESHOLD_EXCEEDED',
            originalEvent: event,
            count,
            timestamp: new Date()
        });
        // In a production environment, you would:
        // 1. Send email/SMS alerts to security team
        // 2. Integrate with incident management systems
        // 3. Automatically block suspicious IPs
        // 4. Trigger additional security measures
    }
}
// Singleton instance
exports.securityMonitoring = new SecurityMonitoringService();
exports.default = SecurityMonitoringService;
//# sourceMappingURL=securityMonitoringService.js.map