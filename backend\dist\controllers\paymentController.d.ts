import { Request, Response } from 'express';
export declare const recordPayment: (req: Request, res: Response) => Promise<void>;
export declare const verifyPayment: (req: Request, res: Response) => Promise<void>;
export declare const getPayment: (req: Request, res: Response) => Promise<void>;
export declare const getInvoicePayments: (req: Request, res: Response) => Promise<void>;
export declare const getUserPayments: (req: Request, res: Response) => Promise<void>;
export declare const manualVerifyPayment: (req: Request, res: Response) => Promise<void>;
export declare const upiWebhook: (req: Request, res: Response) => Promise<void>;
//# sourceMappingURL=paymentController.d.ts.map