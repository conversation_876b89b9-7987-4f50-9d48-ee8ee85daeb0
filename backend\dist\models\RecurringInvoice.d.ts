import mongoose, { Document } from 'mongoose';
export interface IRecurringInvoice extends Document {
    userId: mongoose.Types.ObjectId;
    templateName: string;
    frequency: 'weekly' | 'monthly' | 'quarterly' | 'yearly';
    interval: number;
    startDate: Date;
    endDate?: Date;
    nextGenerationDate: Date;
    invoiceTemplate: {
        invoiceType: 'gst' | 'non-gst';
        customer: {
            name: string;
            email?: string;
            phone?: string;
            address?: string;
            gstNumber?: string;
            stateCode?: string;
        };
        items: Array<{
            description: string;
            quantity: number;
            rate: number;
            unit?: string;
            hsnCode?: string;
            gstRate?: number;
        }>;
        notes?: string;
        terms?: string;
        dueInDays?: number;
    };
    isActive: boolean;
    lastGeneratedDate?: Date;
    totalGenerated: number;
    maxGenerations?: number;
    generatedInvoices: mongoose.Types.ObjectId[];
    createdAt: Date;
    updatedAt: Date;
    calculateNextGenerationDate(): Date;
    shouldGenerate(): boolean;
}
declare const RecurringInvoice: mongoose.Model<IRecurringInvoice, {}, {}, {}, mongoose.Document<unknown, {}, IRecurringInvoice, {}> & IRecurringInvoice & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
export default RecurringInvoice;
//# sourceMappingURL=RecurringInvoice.d.ts.map