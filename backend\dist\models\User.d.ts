import mongoose, { Document } from 'mongoose';
export interface I<PERSON>ser extends Document {
    name: string;
    email: string;
    password: string;
    role: 'user' | 'admin';
    businessName?: string;
    logo?: string;
    gstNumber?: string;
    phone?: string;
    address?: {
        street: string;
        city: string;
        state: string;
        pincode: string;
        country: string;
    };
    upiId?: string;
    bankDetails?: {
        accountNumber?: string;
        ifscCode?: string;
        bankName?: string;
        accountHolderName?: string;
    };
    isEmailVerified: boolean;
    emailVerificationToken?: string;
    emailVerificationExpires?: Date;
    isActive: boolean;
    lastLogin?: Date;
    paymentReminderSettings?: {
        enabled: boolean;
        reminderDays: number[];
        overdueReminderDays: number[];
        maxReminders: number;
    };
    createdAt: Date;
    updatedAt: Date;
    comparePassword(candidatePassword: string): Promise<boolean>;
}
declare const _default: mongoose.Model<IUser, {}, {}, {}, mongoose.Document<unknown, {}, IUser, {}> & IUser & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
export default _default;
//# sourceMappingURL=User.d.ts.map