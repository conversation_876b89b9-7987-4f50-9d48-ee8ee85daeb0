import { Request, Response } from 'express';
/**
 * Invite a team member
 */
export declare const inviteTeamMember: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
/**
 * Get team members
 */
export declare const getTeamMembers: (req: Request, res: Response) => Promise<void>;
/**
 * Update team member
 */
export declare const updateTeamMember: (req: Request, res: Response) => Promise<void>;
/**
 * Remove team member
 */
export declare const removeTeamMember: (req: Request, res: Response) => Promise<void>;
/**
 * Accept team invitation
 */
export declare const acceptInvitation: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
/**
 * Get user's team memberships
 */
export declare const getUserMemberships: (req: Request, res: Response) => Promise<void>;
/**
 * Check user permissions
 */
export declare const checkPermissions: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
/**
 * Get team statistics
 */
export declare const getTeamStats: (req: Request, res: Response) => Promise<void>;
//# sourceMappingURL=teamController.d.ts.map