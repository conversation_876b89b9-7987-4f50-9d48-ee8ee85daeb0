import { Request, Response } from 'express';
export declare const getNotifications: (req: Request, res: Response) => Promise<void>;
export declare const markNotificationRead: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const markAllNotificationsRead: (req: Request, res: Response) => Promise<void>;
export declare const getNotificationPreferences: (req: Request, res: Response) => Promise<void>;
export declare const updateNotificationPreferences: (req: Request, res: Response) => Promise<void>;
export declare const createNotification: (req: Request, res: Response) => Promise<void>;
export declare const getUnreadCount: (req: Request, res: Response) => Promise<void>;
export declare const deleteNotification: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
//# sourceMappingURL=notificationController.d.ts.map