interface UPIValidationResult {
    isValid: boolean;
    exists?: boolean;
    accountName?: string;
    error?: string;
    provider?: string;
}
export declare class UPIValidationService {
    /**
     * Validate UPI ID format
     */
    static validateUPIFormat(upiId: string): boolean;
    /**
     * Enhanced UPI ID validation with existence check
     * Note: This is a mock implementation as real UPI validation requires
     * integration with bank APIs which need special permissions
     */
    static validateUPIExistence(upiId: string): Promise<UPIValidationResult>;
    /**
     * Mock UPI validation (for demonstration)
     * In production, this would call actual bank APIs
     */
    private static mockUPIValidation;
    /**
     * Get provider information
     */
    private static getProviderInfo;
    /**
     * Generate mock account name for testing
     */
    private static generateMockAccountName;
    /**
     * Validate UPI ID and provide suggestions
     */
    static validateWithSuggestions(upiId: string): Promise<{
        isValid: boolean;
        exists?: boolean;
        suggestions?: string[];
        error?: string;
        provider?: string;
    }>;
    /**
     * Generate UPI ID suggestions
     */
    private static generateSuggestions;
    /**
     * Get list of popular UPI providers
     */
    static getPopularProviders(): Array<{
        id: string;
        name: string;
        example: string;
    }>;
}
export default UPIValidationService;
//# sourceMappingURL=upiValidationService.d.ts.map