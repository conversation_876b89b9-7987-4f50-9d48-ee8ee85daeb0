import { Request, Response } from 'express';
export declare const getPlans: (req: Request, res: Response) => Promise<void>;
export declare const getCurrentSubscription: (req: Request, res: Response) => Promise<void>;
export declare const createPaymentOrder: (req: Request, res: Response) => Promise<void>;
export declare const verifyPayment: (req: Request, res: Response) => Promise<void>;
export declare const handlePaymentFailure: (req: Request, res: Response) => Promise<void>;
export declare const changeSubscription: (req: Request, res: Response) => Promise<void>;
export declare const cancelSubscription: (req: Request, res: Response) => Promise<void>;
export declare const getPaymentHistory: (req: Request, res: Response) => Promise<void>;
export declare const startFreeTrial: (req: Request, res: Response) => Promise<void>;
export declare const syncUsage: (req: Request, res: Response) => Promise<void>;
export declare const checkUsageLimit: (req: Request, res: Response) => Promise<void>;
export declare const getRevenueAnalytics: (req: Request, res: Response) => Promise<void>;
//# sourceMappingURL=subscriptionController.d.ts.map