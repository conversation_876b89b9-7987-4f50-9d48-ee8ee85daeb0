{"version": 3, "file": "Notification.js", "sourceRoot": "", "sources": ["../../src/models/Notification.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,qDAAsD;AAiFtD,MAAM,4BAA4B,GAAG,IAAI,iBAAM,CAA0B;IACvE,MAAM,EAAE;QACN,IAAI,EAAE,iBAAM,CAAC,KAAK,CAAC,QAAQ;QAC3B,GAAG,EAAE,MAAM;QACX,QAAQ,EAAE,IAAI;QACd,MAAM,EAAE,IAAI;KACb;IACD,kBAAkB,EAAE;QAClB,IAAI,EAAE,OAAO;QACb,OAAO,EAAE,IAAI;KACd;IACD,YAAY,EAAE;QACZ,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,IAAI;QACV,SAAS,EAAE,IAAI;QACf,KAAK,EAAE;YACL,6CAA6C;YAC7C,4BAA4B;SAC7B;KACF;IACD,mBAAmB,EAAE;QACnB,IAAI,EAAE,OAAO;QACb,OAAO,EAAE,IAAI;KACd;IACD,gBAAgB,EAAE;QAChB,IAAI,EAAE,OAAO;QACb,OAAO,EAAE,IAAI;KACd;IACD,aAAa,EAAE;QACb,IAAI,EAAE,OAAO;QACb,OAAO,EAAE,IAAI;KACd;IACD,eAAe,EAAE;QACf,IAAI,EAAE,OAAO;QACb,OAAO,EAAE,KAAK;KACf;IACD,cAAc,EAAE;QACd,IAAI,EAAE,CAAC;gBACL,IAAI,EAAE,MAAM;gBACZ,GAAG,EAAE,CAAC;gBACN,GAAG,EAAE,GAAG;aACT,CAAC;QACF,SAAS,EAAE;YACT,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,OAAO;YAChB,KAAK,EAAE,CAAC,mCAAmC,EAAE,qBAAqB,CAAC;SACpE;QACD,QAAQ,EAAE;YACR,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,cAAc;SACxB;KACF;IACD,cAAc,EAAE;QACd,IAAI,EAAE,MAAM;QACZ,OAAO,EAAE,CAAC;QACV,GAAG,EAAE,CAAC;QACN,GAAG,EAAE,EAAE;KACR;IACD,UAAU,EAAE;QACV,IAAI,EAAE,OAAO;QACb,OAAO,EAAE,KAAK;KACf;CACF,EAAE;IACD,UAAU,EAAE,IAAI;CACjB,CAAC,CAAC;AAEH,MAAM,kBAAkB,GAAG,IAAI,iBAAM,CAAgB;IACnD,MAAM,EAAE;QACN,IAAI,EAAE,iBAAM,CAAC,KAAK,CAAC,QAAQ;QAC3B,GAAG,EAAE,MAAM;QACX,QAAQ,EAAE,IAAI;KACf;IACD,KAAK,EAAE;QACL,IAAI,EAAE,MAAM;QACZ,QAAQ,EAAE,CAAC,IAAI,EAAE,mBAAmB,CAAC;QACrC,IAAI,EAAE,IAAI;QACV,SAAS,EAAE,CAAC,GAAG,EAAE,oCAAoC,CAAC;KACvD;IACD,OAAO,EAAE;QACP,IAAI,EAAE,MAAM;QACZ,QAAQ,EAAE,CAAC,IAAI,EAAE,qBAAqB,CAAC;QACvC,IAAI,EAAE,IAAI;QACV,SAAS,EAAE,CAAC,IAAI,EAAE,uCAAuC,CAAC;KAC3D;IACD,IAAI,EAAE;QACJ,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,YAAY,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAE,OAAO,CAAC;QAC9D,QAAQ,EAAE,IAAI;KACf;IACD,QAAQ,EAAE;QACR,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,CAAC;QACzC,OAAO,EAAE,QAAQ;KAClB;IACD,aAAa,EAAE;QACb,IAAI,EAAE;YACJ,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,CAAC,YAAY,EAAE,SAAS,EAAE,MAAM,CAAC;SACxC;QACD,EAAE,EAAE;YACF,IAAI,EAAE,iBAAM,CAAC,KAAK,CAAC,QAAQ;SAC5B;KACF;IACD,QAAQ,EAAE;QACR,KAAK,EAAE;YACL,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,KAAK;SACf;QACD,KAAK,EAAE;YACL,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,IAAI;SACd;QACD,IAAI,EAAE;YACJ,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,KAAK;SACf;KACF;IACD,MAAM,EAAE;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM,CAAC;QACxD,OAAO,EAAE,SAAS;KACnB;IACD,MAAM,EAAE;QACN,IAAI,EAAE,IAAI;KACX;IACD,WAAW,EAAE;QACX,IAAI,EAAE,IAAI;KACX;IACD,MAAM,EAAE;QACN,IAAI,EAAE,IAAI;KACX;IACD,YAAY,EAAE;QACZ,OAAO,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE;QACrC,WAAW,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;QAC7B,WAAW,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;QAC7B,WAAW,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;KAChC;IACD,UAAU,EAAE;QACV,IAAI,EAAE,MAAM;QACZ,OAAO,EAAE,CAAC;KACX;IACD,UAAU,EAAE;QACV,IAAI,EAAE,MAAM;QACZ,OAAO,EAAE,CAAC;KACX;IACD,WAAW,EAAE;QACX,IAAI,EAAE,IAAI;KACX;IACD,YAAY,EAAE;QACZ,IAAI,EAAE,IAAI;KACX;IACD,YAAY,EAAE;QACZ,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,IAAI;KACX;CACF,EAAE;IACD,UAAU,EAAE,IAAI;CACjB,CAAC,CAAC;AAEH,iCAAiC;AACjC,kBAAkB,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;AACnD,kBAAkB,CAAC,KAAK,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;AACzD,kBAAkB,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AACrD,kBAAkB,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AAE1C,QAAA,sBAAsB,GAAG,kBAAQ,CAAC,KAAK,CAA0B,wBAAwB,EAAE,4BAA4B,CAAC,CAAC;AACzH,QAAA,YAAY,GAAG,kBAAQ,CAAC,KAAK,CAAgB,cAAc,EAAE,kBAAkB,CAAC,CAAC"}