import mongoose, { Document } from 'mongoose';
export interface ITeamMember extends Document {
    organizationId: mongoose.Types.ObjectId;
    userId: mongoose.Types.ObjectId;
    email: string;
    role: 'owner' | 'admin' | 'manager' | 'user';
    permissions: {
        invoices: {
            create: boolean;
            read: boolean;
            update: boolean;
            delete: boolean;
        };
        customers: {
            create: boolean;
            read: boolean;
            update: boolean;
            delete: boolean;
        };
        documents: {
            create: boolean;
            read: boolean;
            update: boolean;
            delete: boolean;
        };
        analytics: {
            read: boolean;
        };
        settings: {
            read: boolean;
            update: boolean;
        };
        team: {
            invite: boolean;
            manage: boolean;
        };
    };
    status: 'active' | 'inactive' | 'pending';
    invitedBy: mongoose.Types.ObjectId;
    invitedAt: Date;
    joinedAt?: Date;
    lastActive?: Date;
    invitationToken?: string;
    createdAt: Date;
    updatedAt: Date;
    canPerform(resource: string, action: string): boolean;
    canManage(targetRole: string): boolean;
}
declare const _default: mongoose.Model<ITeamMember, {}, {}, {}, mongoose.Document<unknown, {}, ITeamMember, {}> & ITeamMember & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
export default _default;
//# sourceMappingURL=TeamMember.d.ts.map