export interface AnalyticsReport {
    period: string;
    startDate: Date;
    endDate: Date;
    summary: {
        totalInvoices: number;
        totalRevenue: number;
        paidInvoices: number;
        pendingInvoices: number;
        overdueInvoices: number;
        averageInvoiceValue: number;
        paymentRate: number;
    };
    trends: {
        revenueGrowth: number;
        invoiceGrowth: number;
        paymentTimeAverage: number;
    };
    topCustomers: Array<{
        name: string;
        totalAmount: number;
        invoiceCount: number;
    }>;
    insights: string[];
}
export interface CashFlowForecast {
    period: string;
    expectedIncome: number;
    pendingPayments: number;
    overdueAmount: number;
    projectedCashFlow: number;
    riskLevel: 'low' | 'medium' | 'high';
    recommendations: string[];
}
export declare class EnhancedAnalyticsService {
    private static instance;
    private emailService;
    constructor();
    static getInstance(): EnhancedAnalyticsService;
    /**
     * Initialize cron jobs for automated reporting
     */
    private initializeCronJobs;
    /**
     * Generate weekly reports for all users
     */
    generateWeeklyReports(): Promise<void>;
    /**
     * Generate monthly reports for all users
     */
    generateMonthlyReports(): Promise<void>;
    /**
     * Generate analytics report for a user
     */
    generateAnalyticsReport(userId: string, period: 'weekly' | 'monthly' | 'quarterly' | 'yearly'): Promise<AnalyticsReport>;
    /**
     * Generate cash flow forecast
     */
    generateCashFlowForecast(userId: string, days?: number): Promise<CashFlowForecast>;
    /**
     * Process cash flow alerts for all users
     */
    processCashFlowAlerts(): Promise<void>;
    /**
     * Send analytics report via email
     */
    private sendAnalyticsReport;
    /**
     * Send cash flow alert via email
     */
    private sendCashFlowAlert;
    /**
     * Get period dates based on period type
     */
    private getPeriodDates;
    /**
     * Get previous period dates for comparison
     */
    private getPreviousPeriodDates;
    /**
     * Get historical payment rate for a user
     */
    private getHistoricalPaymentRate;
    /**
     * Generate insights based on metrics
     */
    private generateInsights;
    /**
     * Generate cash flow recommendations
     */
    private generateCashFlowRecommendations;
    /**
     * Generate HTML email for analytics report
     */
    private generateReportEmailHTML;
    /**
     * Generate text email for analytics report
     */
    private generateReportEmailText;
    /**
     * Generate HTML email for cash flow alert
     */
    private generateCashFlowAlertHTML;
    /**
     * Generate text email for cash flow alert
     */
    private generateCashFlowAlertText;
}
//# sourceMappingURL=enhancedAnalyticsService.d.ts.map