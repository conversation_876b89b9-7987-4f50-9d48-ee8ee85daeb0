"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const auth_1 = require("../middleware/auth");
const paymentReminderController_1 = require("../controllers/paymentReminderController");
const router = express_1.default.Router();
// All routes require authentication
router.use(auth_1.authenticate);
/**
 * @route   GET /api/payment-reminders/settings
 * @desc    Get payment reminder settings
 * @access  Private
 */
router.get('/settings', paymentReminderController_1.getReminderSettings);
/**
 * @route   PUT /api/payment-reminders/settings
 * @desc    Update payment reminder settings
 * @access  Private
 */
router.put('/settings', paymentReminderController_1.updateReminderSettings);
/**
 * @route   POST /api/payment-reminders/send/:invoiceId
 * @desc    Send manual payment reminder for an invoice
 * @access  Private
 */
router.post('/send/:invoiceId', paymentReminderController_1.sendManualReminder);
/**
 * @route   GET /api/payment-reminders/stats
 * @desc    Get payment reminder statistics
 * @access  Private
 */
router.get('/stats', paymentReminderController_1.getReminderStats);
/**
 * @route   GET /api/payment-reminders/upcoming
 * @desc    Get upcoming payment reminders
 * @access  Private
 */
router.get('/upcoming', paymentReminderController_1.getUpcomingReminders);
/**
 * @route   GET /api/payment-reminders/overdue
 * @desc    Get overdue invoices
 * @access  Private
 */
router.get('/overdue', paymentReminderController_1.getOverdueInvoices);
exports.default = router;
//# sourceMappingURL=paymentReminders.js.map