"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const otpController_1 = require("../controllers/otpController");
const auth_1 = require("../middleware/auth");
// Import express-validator using require for compatibility
const { body, validationResult } = require('express-validator');
const router = express_1.default.Router();
// Validation middleware
const validateRequest = (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            success: false,
            message: 'Validation failed',
            errors: errors.array()
        });
    }
    next();
};
// Public routes (no authentication required)
/**
 * @route   POST /api/otp/send-login
 * @desc    Send OTP for login
 * @access  Public
 */
router.post('/send-login', [
    body('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email address')
], validateRequest, otpController_1.sendLoginOTP);
/**
 * @route   POST /api/otp/verify-login
 * @desc    Verify login OTP and authenticate user
 * @access  Public
 */
router.post('/verify-login', [
    body('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email address'),
    body('otp')
        .isLength({ min: 6, max: 6 })
        .isNumeric()
        .withMessage('OTP must be a 6-digit number')
], validateRequest, otpController_1.verifyLoginOTP);
/**
 * @route   POST /api/otp/send-password-reset
 * @desc    Send OTP for password reset
 * @access  Public
 */
router.post('/send-password-reset', [
    body('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email address')
], validateRequest, otpController_1.sendPasswordResetOTP);
/**
 * @route   POST /api/otp/verify-password-reset
 * @desc    Verify password reset OTP
 * @access  Public
 */
router.post('/verify-password-reset', [
    body('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email address'),
    body('otp')
        .isLength({ min: 6, max: 6 })
        .isNumeric()
        .withMessage('OTP must be a 6-digit number')
], validateRequest, otpController_1.verifyPasswordResetOTP);
// Protected routes (authentication required)
router.use(auth_1.authenticate);
/**
 * @route   POST /api/otp/send-operation
 * @desc    Send OTP for sensitive operations
 * @access  Private
 */
router.post('/send-operation', [
    body('operation')
        .isIn(['profile-update', 'sensitive-operation'])
        .withMessage('Invalid operation type')
], validateRequest, otpController_1.sendOperationOTP);
/**
 * @route   POST /api/otp/verify-operation
 * @desc    Verify operation OTP
 * @access  Private
 */
router.post('/verify-operation', [
    body('otp')
        .isLength({ min: 6, max: 6 })
        .isNumeric()
        .withMessage('OTP must be a 6-digit number'),
    body('operation')
        .isIn(['profile-update', 'sensitive-operation'])
        .withMessage('Invalid operation type')
], validateRequest, otpController_1.verifyOperationOTP);
/**
 * @route   GET /api/otp/stats
 * @desc    Get OTP statistics (admin only)
 * @access  Private (Admin)
 */
router.get('/stats', otpController_1.getOTPStats);
exports.default = router;
//# sourceMappingURL=otpRoutes.js.map