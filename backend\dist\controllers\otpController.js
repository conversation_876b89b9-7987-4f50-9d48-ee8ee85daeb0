"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getOTPStats = exports.verifyOperationOTP = exports.sendOperationOTP = exports.verifyPasswordResetOTP = exports.sendPasswordResetOTP = exports.verifyLoginOTP = exports.sendLoginOTP = void 0;
const otpService_1 = __importDefault(require("../services/otpService"));
const User_1 = __importDefault(require("../models/User"));
/**
 * Send OTP for login
 */
const sendLoginOTP = async (req, res) => {
    try {
        const { email } = req.body;
        if (!email) {
            res.status(400).json({
                success: false,
                message: 'Email is required'
            });
            return;
        }
        // Check if user exists
        const user = await User_1.default.findOne({ email });
        if (!user) {
            res.status(404).json({
                success: false,
                message: 'No account found with this email address'
            });
            return;
        }
        // Send OTP
        const result = await otpService_1.default.sendOTP(email, user.name, 'login');
        if (result.success) {
            res.status(200).json({
                success: true,
                message: result.message,
                data: {
                    email: email,
                    otpId: result.otpId
                }
            });
        }
        else {
            res.status(400).json({
                success: false,
                message: result.message
            });
        }
    }
    catch (error) {
        console.error('Send login OTP error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};
exports.sendLoginOTP = sendLoginOTP;
/**
 * Verify login OTP
 */
const verifyLoginOTP = async (req, res) => {
    try {
        const { email, otp } = req.body;
        if (!email || !otp) {
            res.status(400).json({
                success: false,
                message: 'Email and OTP are required'
            });
            return;
        }
        // Verify OTP
        const verification = await otpService_1.default.verifyOTP(email, otp, 'login');
        if (!verification.isValid) {
            res.status(400).json({
                success: false,
                message: verification.message,
                remainingAttempts: verification.remainingAttempts
            });
            return;
        }
        // Get user details
        const user = await User_1.default.findOne({ email });
        if (!user) {
            res.status(404).json({
                success: false,
                message: 'User not found'
            });
            return;
        }
        // Generate JWT tokens (you'll need to import these from your auth utils)
        const { generateToken, generateRefreshToken } = require('../utils/jwt');
        const token = generateToken(user);
        const refreshToken = generateRefreshToken(user);
        // Update last login
        user.lastLogin = new Date();
        await user.save();
        res.status(200).json({
            success: true,
            message: 'Login successful',
            data: {
                user: {
                    id: user._id,
                    name: user.name,
                    email: user.email,
                    role: user.role,
                    businessName: user.businessName,
                    gstNumber: user.gstNumber,
                    phone: user.phone,
                    address: user.address,
                    isEmailVerified: user.isEmailVerified,
                    lastLogin: user.lastLogin
                },
                token,
                refreshToken
            }
        });
    }
    catch (error) {
        console.error('Verify login OTP error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};
exports.verifyLoginOTP = verifyLoginOTP;
/**
 * Send OTP for password reset
 */
const sendPasswordResetOTP = async (req, res) => {
    try {
        const { email } = req.body;
        if (!email) {
            res.status(400).json({
                success: false,
                message: 'Email is required'
            });
            return;
        }
        // Check if user exists
        const user = await User_1.default.findOne({ email });
        if (!user) {
            // Don't reveal if email exists or not for security
            res.status(200).json({
                success: true,
                message: 'If an account with this email exists, an OTP has been sent.'
            });
            return;
        }
        // Send OTP
        const result = await otpService_1.default.sendOTP(email, user.name, 'password-reset');
        res.status(200).json({
            success: true,
            message: 'If an account with this email exists, an OTP has been sent.',
            data: result.success ? { otpId: result.otpId } : undefined
        });
    }
    catch (error) {
        console.error('Send password reset OTP error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};
exports.sendPasswordResetOTP = sendPasswordResetOTP;
/**
 * Verify password reset OTP
 */
const verifyPasswordResetOTP = async (req, res) => {
    try {
        const { email, otp } = req.body;
        if (!email || !otp) {
            res.status(400).json({
                success: false,
                message: 'Email and OTP are required'
            });
            return;
        }
        // Verify OTP
        const verification = await otpService_1.default.verifyOTP(email, otp, 'password-reset');
        if (!verification.isValid) {
            res.status(400).json({
                success: false,
                message: verification.message,
                remainingAttempts: verification.remainingAttempts
            });
            return;
        }
        // Generate a temporary token for password reset
        const crypto = require('crypto');
        const resetToken = crypto.randomBytes(32).toString('hex');
        const resetExpires = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes
        // Update user with reset token
        await User_1.default.findOneAndUpdate({ email }, {
            passwordResetToken: resetToken,
            passwordResetExpires: resetExpires
        });
        res.status(200).json({
            success: true,
            message: 'OTP verified successfully. You can now reset your password.',
            data: {
                resetToken
            }
        });
    }
    catch (error) {
        console.error('Verify password reset OTP error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};
exports.verifyPasswordResetOTP = verifyPasswordResetOTP;
/**
 * Send OTP for sensitive operations (profile updates, etc.)
 */
const sendOperationOTP = async (req, res) => {
    try {
        const user = req.user;
        const { operation } = req.body;
        if (!user) {
            res.status(401).json({
                success: false,
                message: 'User not authenticated'
            });
            return;
        }
        const validOperations = ['profile-update', 'sensitive-operation'];
        if (!operation || !validOperations.includes(operation)) {
            res.status(400).json({
                success: false,
                message: 'Valid operation type is required'
            });
            return;
        }
        // Send OTP
        const result = await otpService_1.default.sendOTP(user.email, user.name, operation);
        if (result.success) {
            res.status(200).json({
                success: true,
                message: result.message,
                data: {
                    otpId: result.otpId
                }
            });
        }
        else {
            res.status(400).json({
                success: false,
                message: result.message
            });
        }
    }
    catch (error) {
        console.error('Send operation OTP error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};
exports.sendOperationOTP = sendOperationOTP;
/**
 * Verify operation OTP
 */
const verifyOperationOTP = async (req, res) => {
    try {
        const user = req.user;
        const { otp, operation } = req.body;
        if (!user) {
            res.status(401).json({
                success: false,
                message: 'User not authenticated'
            });
            return;
        }
        if (!otp || !operation) {
            res.status(400).json({
                success: false,
                message: 'OTP and operation type are required'
            });
            return;
        }
        // Verify OTP
        const verification = await otpService_1.default.verifyOTP(user.email, otp, operation);
        if (!verification.isValid) {
            res.status(400).json({
                success: false,
                message: verification.message,
                remainingAttempts: verification.remainingAttempts
            });
            return;
        }
        res.status(200).json({
            success: true,
            message: 'OTP verified successfully. You can proceed with the operation.',
            data: {
                verified: true,
                operation
            }
        });
    }
    catch (error) {
        console.error('Verify operation OTP error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};
exports.verifyOperationOTP = verifyOperationOTP;
/**
 * Get OTP statistics (admin only)
 */
const getOTPStats = async (req, res) => {
    try {
        const user = req.user;
        if (!user || user.role !== 'admin') {
            res.status(403).json({
                success: false,
                message: 'Admin access required'
            });
            return;
        }
        const stats = otpService_1.default.getStats();
        res.status(200).json({
            success: true,
            message: 'OTP statistics retrieved successfully',
            data: stats
        });
    }
    catch (error) {
        console.error('Get OTP stats error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};
exports.getOTPStats = getOTPStats;
//# sourceMappingURL=otpController.js.map