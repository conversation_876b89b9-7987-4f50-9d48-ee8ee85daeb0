import { RateLimiterMemory } from 'rate-limiter-flexible';
import { Request, Response, NextFunction } from 'express';
import winston from 'winston';
declare const securityLogger: winston.Logger;
declare const rateLimiters: {
    general: import("express-rate-limit").RateLimitRequestHandler;
    auth: import("express-rate-limit").RateLimitRequestHandler;
    otp: import("express-rate-limit").RateLimitRequestHandler;
    api: import("express-rate-limit").RateLimitRequestHandler;
};
declare const speedLimiter: import("express-rate-limit").RateLimitRequestHandler;
declare const advancedRateLimiter: RateLimiterMemory;
declare const bruteForceProtection: RateLimiterMemory;
export declare const securityHeaders: (req: Request, res: Response, next: NextFunction) => void;
export declare const requestLogger: (req: Request, res: Response, next: NextFunction) => void;
export declare const suspiciousActivityDetection: (req: Request, res: Response, next: NextFunction) => Response<any, Record<string, any>> | undefined;
export declare const advancedBruteForceProtection: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export { rateLimiters, speedLimiter, advancedRateLimiter, bruteForceProtection, securityLogger };
//# sourceMappingURL=security.d.ts.map