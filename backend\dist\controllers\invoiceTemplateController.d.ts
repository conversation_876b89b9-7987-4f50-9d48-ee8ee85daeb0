import { Request, Response } from 'express';
export declare const getInvoiceTemplates: (req: Request, res: Response) => Promise<void>;
export declare const getInvoiceTemplate: (req: Request, res: Response) => Promise<void>;
export declare const createInvoiceTemplate: (req: Request, res: Response) => Promise<void>;
export declare const updateInvoiceTemplate: (req: Request, res: Response) => Promise<void>;
export declare const deleteInvoiceTemplate: (req: Request, res: Response) => Promise<void>;
export declare const createInvoiceFromTemplate: (req: Request, res: Response) => Promise<void>;
export declare const setDefaultTemplate: (req: Request, res: Response) => Promise<void>;
export declare const getTemplateCategories: (req: Request, res: Response) => Promise<void>;
//# sourceMappingURL=invoiceTemplateController.d.ts.map