"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const batchOperationsController_1 = require("../controllers/batchOperationsController");
const auth_1 = require("../middleware/auth");
const router = express_1.default.Router();
// All routes require authentication
router.use(auth_1.authenticate);
// Batch operations
router.post('/invoices/create', batchOperationsController_1.createBatchInvoices);
router.post('/invoices/send-emails', batchOperationsController_1.sendBatchEmails);
router.patch('/invoices/update-status', batchOperationsController_1.updateBatchInvoiceStatus);
router.delete('/invoices/delete', batchOperationsController_1.deleteBatchInvoices);
// Operation history
router.get('/history', batchOperationsController_1.getBatchOperationHistory);
exports.default = router;
//# sourceMappingURL=batchOperationsRoutes.js.map