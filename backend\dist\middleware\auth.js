"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.optionalAuth = exports.authorize = exports.authenticate = void 0;
const jwt_1 = require("../utils/jwt");
const User_1 = __importDefault(require("../models/User"));
const tokenBlacklistService_1 = require("../services/tokenBlacklistService");
const securityMonitoringService_1 = require("../services/securityMonitoringService");
const adminAuth_1 = require("../utils/adminAuth");
const authenticate = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            res.status(401).json({
                success: false,
                message: 'Access denied. No token provided.'
            });
            return;
        }
        const token = authHeader.substring(7); // Remove 'Bearer ' prefix
        if (!token) {
            res.status(401).json({
                success: false,
                message: 'Access denied. Invalid token format.'
            });
            return;
        }
        // Check if token is blacklisted
        const isBlacklisted = await tokenBlacklistService_1.tokenBlacklist.isTokenBlacklisted(token);
        if (isBlacklisted) {
            securityMonitoringService_1.securityMonitoring.logSecurityEvent({
                type: securityMonitoringService_1.SecurityEventType.UNAUTHORIZED_ACCESS,
                severity: securityMonitoringService_1.SecuritySeverity.MEDIUM,
                message: 'Attempt to use blacklisted token',
                ip: req.ip || 'unknown',
                userAgent: req.get('User-Agent'),
                path: req.path,
                method: req.method,
                timestamp: new Date(),
                metadata: { token: token.substring(0, 20) + '...' }
            });
            res.status(401).json({
                success: false,
                message: 'Token has been invalidated. Please login again.'
            });
            return;
        }
        // Verify token
        const decoded = (0, jwt_1.verifyToken)(token);
        // Check if user is globally blacklisted
        const isUserBlacklisted = await tokenBlacklistService_1.tokenBlacklist.isUserBlacklisted(decoded.userId);
        if (isUserBlacklisted) {
            res.status(401).json({
                success: false,
                message: 'Account access has been suspended. Please contact support.'
            });
            return;
        }
        // Check if this is an admin user
        if ((0, adminAuth_1.isAdminUserId)(decoded.userId)) {
            // Handle admin user
            const adminUser = (0, adminAuth_1.createAdminUserObject)();
            req.user = adminUser;
        }
        else {
            // Get regular user from database
            const user = await User_1.default.findById(decoded.userId).select('-password');
            if (!user) {
                res.status(401).json({
                    success: false,
                    message: 'Token is valid but user not found.'
                });
                return;
            }
            if (!user.isActive) {
                res.status(401).json({
                    success: false,
                    message: 'Account is deactivated. Please contact support.'
                });
                return;
            }
            // Attach user to request object
            req.user = user;
        }
        next();
    }
    catch (error) {
        console.error('Authentication error:', error);
        res.status(401).json({
            success: false,
            message: 'Invalid or expired token.'
        });
    }
};
exports.authenticate = authenticate;
const authorize = (...roles) => {
    return (req, res, next) => {
        if (!req.user) {
            res.status(401).json({
                success: false,
                message: 'Authentication required.'
            });
            return;
        }
        if (!roles.includes(req.user.role)) {
            res.status(403).json({
                success: false,
                message: 'Access denied. Insufficient permissions.'
            });
            return;
        }
        next();
    };
};
exports.authorize = authorize;
const optionalAuth = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        if (authHeader && authHeader.startsWith('Bearer ')) {
            const token = authHeader.substring(7);
            if (token) {
                const decoded = (0, jwt_1.verifyToken)(token);
                if ((0, adminAuth_1.isAdminUserId)(decoded.userId)) {
                    // Handle admin user
                    const adminUser = (0, adminAuth_1.createAdminUserObject)();
                    req.user = adminUser;
                }
                else {
                    // Handle regular user
                    const user = await User_1.default.findById(decoded.userId).select('-password');
                    if (user && user.isActive) {
                        req.user = user;
                    }
                }
            }
        }
        next();
    }
    catch (error) {
        // For optional auth, we don't throw errors, just continue without user
        next();
    }
};
exports.optionalAuth = optionalAuth;
//# sourceMappingURL=auth.js.map