"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const multer_1 = __importDefault(require("multer"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const auth_1 = require("../middleware/auth");
const router = express_1.default.Router();
// Create uploads directory if it doesn't exist
const uploadsDir = path_1.default.join(process.cwd(), 'uploads');
const logosDir = path_1.default.join(uploadsDir, 'logos');
if (!fs_1.default.existsSync(uploadsDir)) {
    fs_1.default.mkdirSync(uploadsDir, { recursive: true });
}
if (!fs_1.default.existsSync(logosDir)) {
    fs_1.default.mkdirSync(logosDir, { recursive: true });
}
// Configure multer for logo uploads
const logoStorage = multer_1.default.diskStorage({
    destination: (req, file, cb) => {
        cb(null, logosDir);
    },
    filename: (req, file, cb) => {
        const userId = req.user?._id;
        const ext = path_1.default.extname(file.originalname);
        const filename = `logo-${userId}-${Date.now()}${ext}`;
        cb(null, filename);
    }
});
const logoUpload = (0, multer_1.default)({
    storage: logoStorage,
    limits: {
        fileSize: 5 * 1024 * 1024, // 5MB limit
    },
    fileFilter: (req, file, cb) => {
        // Check file type
        const allowedTypes = /jpeg|jpg|png|gif|svg|webp/;
        const extname = allowedTypes.test(path_1.default.extname(file.originalname).toLowerCase());
        const mimetype = allowedTypes.test(file.mimetype);
        if (mimetype && extname) {
            return cb(null, true);
        }
        else {
            cb(new Error('Only image files are allowed (jpeg, jpg, png, gif, svg, webp)'));
        }
    }
});
// Upload logo endpoint
router.post('/logo', auth_1.authenticate, logoUpload.single('logo'), async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({
                success: false,
                message: 'No file uploaded'
            });
        }
        // Delete old logo if exists
        const user = req.user;
        if (!user) {
            return res.status(401).json({
                success: false,
                message: 'User not authenticated'
            });
        }
        if (user.logo) {
            const oldLogoPath = path_1.default.join(process.cwd(), user.logo);
            if (fs_1.default.existsSync(oldLogoPath)) {
                fs_1.default.unlinkSync(oldLogoPath);
            }
        }
        // Return the relative path for storing in database
        const logoPath = `uploads/logos/${req.file.filename}`;
        // Update user profile with new logo path
        const User = require('../models/User').default;
        await User.findByIdAndUpdate(user._id, { logo: logoPath });
        res.status(200).json({
            success: true,
            message: 'Logo uploaded successfully',
            data: {
                logoPath,
                filename: req.file.filename,
                originalName: req.file.originalname,
                size: req.file.size
            }
        });
    }
    catch (error) {
        console.error('Logo upload error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to upload logo'
        });
    }
});
// Note: File serving is now handled by express.static middleware in main app
// Delete logo endpoint
router.delete('/logo', auth_1.authenticate, async (req, res) => {
    try {
        const User = require('../models/User').default;
        const userId = req.user?._id;
        if (!userId) {
            return res.status(401).json({
                success: false,
                message: 'User not authenticated'
            });
        }
        // Get user from database to get current logo
        const user = await User.findById(userId);
        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }
        if (!user.logo) {
            return res.status(400).json({
                success: false,
                message: 'No logo to delete'
            });
        }
        // Delete file from filesystem
        const logoPath = path_1.default.join(process.cwd(), user.logo);
        if (fs_1.default.existsSync(logoPath)) {
            fs_1.default.unlinkSync(logoPath);
        }
        // Update user profile to remove logo reference
        await User.findByIdAndUpdate(userId, { $unset: { logo: 1 } });
        res.status(200).json({
            success: true,
            message: 'Logo deleted successfully'
        });
    }
    catch (error) {
        console.error('Logo delete error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to delete logo'
        });
    }
});
exports.default = router;
//# sourceMappingURL=uploadRoutes.js.map