"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const paymentController_1 = require("../controllers/paymentController");
const auth_1 = require("../middleware/auth");
const router = express_1.default.Router();
// Record a payment
router.post('/record', auth_1.authenticate, paymentController_1.recordPayment);
// Verify a payment
router.post('/verify', auth_1.authenticate, paymentController_1.verifyPayment);
// Get payment details
router.get('/:paymentId', auth_1.authenticate, paymentController_1.getPayment);
// Get payments for an invoice
router.get('/invoice/:invoiceId', auth_1.authenticate, paymentController_1.getInvoicePayments);
// Get user payments
router.get('/user/all', auth_1.authenticate, paymentController_1.getUserPayments);
// Manual verify payment (admin)
router.post('/:paymentId/verify-manual', auth_1.authenticate, paymentController_1.manualVerifyPayment);
// UPI webhook (no auth required for webhooks)
router.post('/webhook/upi', paymentController_1.upiWebhook);
exports.default = router;
//# sourceMappingURL=paymentRoutes.js.map