{"version": 3, "file": "inputSanitization.js", "sourceRoot": "", "sources": ["../../src/middleware/inputSanitization.ts"], "names": [], "mappings": ";;;AACA,qFAAgH;AAEhH,2DAA2D;AAC3D,MAAM,gBAAgB,GAAG,CAAC,GAAY,EAAE,EAAE,CAAC,CAAC;IAC1C,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI;IACnB,KAAK,EAAE,GAAG,EAAE,CAAC,EAAE;CAChB,CAAC,CAAC;AAEH,MAAM,IAAI,GAAG,CAAC,KAAa,EAAE,EAAE,CAAC,CAAC;IAC/B,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC;IAC1B,cAAc,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC;IACjC,WAAW,EAAE,CAAC,GAAS,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC;IACvC,QAAQ,EAAE,CAAC,OAAa,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC;IACxC,OAAO,EAAE,CAAC,OAAa,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC;IACvC,IAAI,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC;IACvB,QAAQ,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC;IAC3B,SAAS,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC;CAC7B,CAAC,CAAC;AAEH,MAAM,KAAK,GAAG,IAAI,CAAC;AACnB,MAAM,KAAK,GAAG,IAAI,CAAC;AAEnB,yCAAyC;AACzC,MAAM,kBAAkB,GAAG;IACzB,aAAa,EAAE;QACb,4EAA4E;QAC5E,8BAA8B;QAC9B,gBAAgB;QAChB,oBAAoB;QACpB,wDAAwD;KACzD;IACD,GAAG,EAAE;QACH,qDAAqD;QACrD,eAAe;QACf,aAAa;QACb,cAAc;QACd,eAAe;QACf,eAAe;QACf,mBAAmB;QACnB,WAAW;QACX,WAAW;QACX,UAAU;KACX;IACD,cAAc,EAAE;QACd,QAAQ;QACR,WAAW;QACX,YAAY;QACZ,WAAW;QACX,eAAe;QACf,gBAAgB;KACjB;IACD,iBAAiB,EAAE;QACjB,oBAAoB;QACpB,2CAA2C;QAC3C,yBAAyB;KAC1B;IACD,cAAc,EAAE;QACd,mBAAmB;KACpB;CACF,CAAC;AAEF,uBAAuB;AACvB,MAAM,kBAAkB,GAAG;IACzB,MAAM,EAAE,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC;IAC9D,SAAS,EAAE;QACT,iBAAiB;QACjB,oBAAoB;QACpB,yEAAyE;QACzE,0BAA0B;QAC1B,mEAAmE;QACnE,YAAY;QACZ,UAAU;KACX;CACF,CAAC;AAEF;;GAEG;AACI,MAAM,cAAc,GAAG,CAAC,KAAa,EAAU,EAAE;IACtD,IAAI,OAAO,KAAK,KAAK,QAAQ;QAAE,OAAO,EAAE,CAAC;IAEzC,OAAO,KAAK;SACT,IAAI,EAAE;SACN,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,wBAAwB;SAC7C,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC,8BAA8B;SAC3D,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC,4BAA4B;SACvD,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC,wBAAwB;SACnD,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAAC,CAAC,4BAA4B;AAClE,CAAC,CAAC;AAVW,QAAA,cAAc,kBAUzB;AAEF;;GAEG;AACI,MAAM,uBAAuB,GAAG,CAAC,KAAa,EAAyD,EAAE;IAC9G,IAAI,OAAO,KAAK,KAAK,QAAQ;QAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;IAEpE,KAAK,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE,CAAC;QAClE,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;gBACxB,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;YAC/D,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;AACvC,CAAC,CAAC;AAZW,QAAA,uBAAuB,2BAYlC;AAEF;;GAEG;AACI,MAAM,aAAa,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;IACrF,IAAI,CAAC;QACH,gBAAgB;QAChB,IAAI,GAAG,CAAC,IAAI,IAAI,OAAO,GAAG,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC7C,GAAG,CAAC,IAAI,GAAG,cAAc,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QAC3C,CAAC;QAED,4BAA4B;QAC5B,IAAI,GAAG,CAAC,KAAK,IAAI,OAAO,GAAG,CAAC,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC/C,GAAG,CAAC,KAAK,GAAG,cAAc,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QAC7C,CAAC;QAED,0BAA0B;QAC1B,IAAI,GAAG,CAAC,MAAM,IAAI,OAAO,GAAG,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;YACjD,GAAG,CAAC,MAAM,GAAG,cAAc,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;QAClD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,wBAAwB;YACjC,KAAK,EAAE,0BAA0B;SAClC,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC;AA1BW,QAAA,aAAa,iBA0BxB;AAEF;;GAEG;AACH,MAAM,cAAc,GAAG,CAAC,GAAQ,EAAE,GAAY,EAAO,EAAE;IACrD,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,SAAS;QAAE,OAAO,GAAG,CAAC;IAElD,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;QAC5B,+BAA+B;QAC/B,MAAM,gBAAgB,GAAG,IAAA,+BAAuB,EAAC,GAAG,CAAC,CAAC;QACtD,IAAI,gBAAgB,CAAC,QAAQ,EAAE,CAAC;YAC9B,qBAAqB;YACrB,8CAAkB,CAAC,mBAAmB,CACpC,GAAG,EACH,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,6CAAiB,CAAC,qBAAqB,CAAC,CAAC,CAAC,6CAAiB,CAAC,WAAW,EAC/G,gBAAgB,CAAC,OAAO,IAAI,SAAS,CACtC,CAAC;YAEF,MAAM,IAAI,KAAK,CAAC,+BAA+B,gBAAgB,CAAC,IAAI,EAAE,CAAC,CAAC;QAC1E,CAAC;QAED,OAAO,IAAA,sBAAc,EAAC,GAAG,CAAC,CAAC;IAC7B,CAAC;IAED,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;QACvB,OAAO,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;IACpD,CAAC;IAED,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;QAC5B,MAAM,SAAS,GAAQ,EAAE,CAAC;QAC1B,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;YAC/C,MAAM,YAAY,GAAG,IAAA,sBAAc,EAAC,GAAG,CAAC,CAAC;YACzC,SAAS,CAAC,YAAY,CAAC,GAAG,cAAc,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QACvD,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,OAAO,GAAG,CAAC;AACb,CAAC,CAAC;AAEF;;GAEG;AACI,MAAM,kBAAkB,GAAG,CAAC,eAA+C,KAAK,EAAE,EAAE;IACzF,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;QAC/D,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;YAC5B,OAAO,IAAI,EAAE,CAAC;QAChB,CAAC;QAED,MAAM,KAAK,GAAU,EAAE,CAAC;QACxB,IAAI,GAAG,CAAC,KAAK,EAAE,CAAC;YACd,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC7B,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;YAC3B,CAAC;iBAAM,CAAC;gBACN,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YACxB,CAAC;QACH,CAAC;QACD,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC;YACb,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACvB,CAAC;QAED,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,IAAI,CAAC,IAAI;gBAAE,SAAS;YAEpB,kBAAkB;YAClB,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,UAAU,CAAC,CAAC,CAAC,eAAe;YAClF,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,GAAG,OAAO,EAAE,CAAC;gBACrC,8CAAkB,CAAC,gBAAgB,CAAC;oBAClC,IAAI,EAAE,6CAAiB,CAAC,qBAAqB;oBAC7C,QAAQ,EAAE,4CAAgB,CAAC,MAAM;oBACjC,OAAO,EAAE,4BAA4B,IAAI,CAAC,IAAI,QAAQ;oBACtD,EAAE,EAAE,GAAG,CAAC,EAAE,IAAI,SAAS;oBACvB,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;oBAChC,MAAM,EAAG,GAAW,CAAC,IAAI,EAAE,EAAE;oBAC7B,IAAI,EAAE,GAAG,CAAC,IAAI;oBACd,MAAM,EAAE,GAAG,CAAC,MAAM;oBAClB,SAAS,EAAE,IAAI,IAAI,EAAE;oBACrB,QAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,YAAY,EAAE,QAAQ,EAAE,IAAI,CAAC,IAAI,EAAE;iBAC/D,CAAC,CAAC;gBAEH,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,6CAA6C,OAAO,QAAQ;oBACrE,KAAK,EAAE,oBAAoB;iBAC5B,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,kBAAkB;YAClB,IAAI,gBAAgB,GAAa,EAAE,CAAC;YACpC,IAAI,YAAY,KAAK,QAAQ,EAAE,CAAC;gBAC9B,gBAAgB,GAAG,kBAAkB,CAAC,MAAM,CAAC;YAC/C,CAAC;iBAAM,IAAI,YAAY,KAAK,WAAW,EAAE,CAAC;gBACxC,gBAAgB,GAAG,kBAAkB,CAAC,SAAS,CAAC;YAClD,CAAC;iBAAM,CAAC;gBACN,gBAAgB,GAAG,CAAC,GAAG,kBAAkB,CAAC,MAAM,EAAE,GAAG,kBAAkB,CAAC,SAAS,CAAC,CAAC;YACrF,CAAC;YAED,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC/D,8CAAkB,CAAC,gBAAgB,CAAC;oBAClC,IAAI,EAAE,6CAAiB,CAAC,qBAAqB;oBAC7C,QAAQ,EAAE,4CAAgB,CAAC,IAAI;oBAC/B,OAAO,EAAE,+BAA+B,IAAI,CAAC,QAAQ,EAAE;oBACvD,EAAE,EAAE,GAAG,CAAC,EAAE,IAAI,SAAS;oBACvB,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;oBAChC,MAAM,EAAG,GAAW,CAAC,IAAI,EAAE,EAAE;oBAC7B,IAAI,EAAE,GAAG,CAAC,IAAI;oBACd,MAAM,EAAE,GAAG,CAAC,MAAM;oBAClB,SAAS,EAAE,IAAI,IAAI,EAAE;oBACrB,QAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,YAAY,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE;iBACnE,CAAC,CAAC;gBAEH,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,aAAa,IAAI,CAAC,QAAQ,iBAAiB;oBACpD,KAAK,EAAE,mBAAmB;iBAC3B,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,wCAAwC;YACxC,MAAM,gBAAgB,GAAG,IAAA,+BAAuB,EAAC,IAAI,CAAC,YAAY,IAAI,EAAE,CAAC,CAAC;YAC1E,IAAI,gBAAgB,CAAC,QAAQ,EAAE,CAAC;gBAC9B,8CAAkB,CAAC,gBAAgB,CAAC;oBAClC,IAAI,EAAE,6CAAiB,CAAC,qBAAqB;oBAC7C,QAAQ,EAAE,4CAAgB,CAAC,IAAI;oBAC/B,OAAO,EAAE,kCAAkC,IAAI,CAAC,YAAY,EAAE;oBAC9D,EAAE,EAAE,GAAG,CAAC,EAAE,IAAI,SAAS;oBACvB,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;oBAChC,MAAM,EAAG,GAAW,CAAC,IAAI,EAAE,EAAE;oBAC7B,IAAI,EAAE,GAAG,CAAC,IAAI;oBACd,MAAM,EAAE,GAAG,CAAC,MAAM;oBAClB,SAAS,EAAE,IAAI,IAAI,EAAE;oBACrB,QAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,YAAY,EAAE,OAAO,EAAE,gBAAgB,CAAC,OAAO,EAAE;iBAC7E,CAAC,CAAC;gBAEH,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,2BAA2B;oBACpC,KAAK,EAAE,kBAAkB;iBAC1B,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;QACH,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AAxGW,QAAA,kBAAkB,sBAwG7B;AAEF;;GAEG;AACI,MAAM,sBAAsB,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;IAC9F,MAAM,MAAM,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC;IAErC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC;QACtB,kDAAkD;QAClD,8CAAkB,CAAC,gBAAgB,CAAC;YAClC,IAAI,EAAE,6CAAiB,CAAC,mBAAmB;YAC3C,QAAQ,EAAE,4CAAgB,CAAC,GAAG;YAC9B,OAAO,EAAE,yBAAyB;YAClC,EAAE,EAAE,GAAG,CAAC,EAAE,IAAI,SAAS;YACvB,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;YAChC,MAAM,EAAG,GAAW,CAAC,IAAI,EAAE,EAAE;YAC7B,IAAI,EAAE,GAAG,CAAC,IAAI;YACd,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,QAAQ,EAAE,EAAE,gBAAgB,EAAE,MAAM,CAAC,KAAK,EAAE,EAAE;SAC/C,CAAC,CAAC;QAEH,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,mBAAmB;YAC5B,MAAM,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,CAAC;gBAC1C,KAAK,EAAE,KAAK,CAAC,KAAK;gBAClB,OAAO,EAAE,KAAK,CAAC,GAAG;gBAClB,KAAK,EAAE,KAAK,CAAC,KAAK;aACnB,CAAC,CAAC;SACJ,CAAC,CAAC;QACH,OAAO;IACT,CAAC;IAED,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AA/BW,QAAA,sBAAsB,0BA+BjC;AAEF,2BAA2B;AACd,QAAA,iBAAiB,GAAG;IAC/B,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC;SACjB,OAAO,EAAE;SACT,WAAW,CAAC,sCAAsC,CAAC;SACnD,cAAc,EAAE;SAChB,QAAQ,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;SACtB,WAAW,CAAC,2BAA2B,CAAC;IAE3C,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC;SACvB,QAAQ,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;SAC9B,WAAW,CAAC,+CAA+C,CAAC;SAC5D,OAAO,CAAC,iEAAiE,CAAC;SAC1E,WAAW,CAAC,kHAAkH,CAAC;IAElI,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC;SACf,IAAI,EAAE;SACN,QAAQ,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC;SAC7B,WAAW,CAAC,0CAA0C,CAAC;SACvD,OAAO,CAAC,eAAe,CAAC;SACxB,WAAW,CAAC,0CAA0C,CAAC;IAE1D,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC;SACjB,QAAQ,EAAE;SACV,OAAO,CAAC,cAAc,CAAC;SACvB,WAAW,CAAC,qDAAqD,CAAC;IAErE,SAAS,EAAE,IAAI,CAAC,WAAW,CAAC;SACzB,QAAQ,EAAE;SACV,OAAO,CAAC,2DAA2D,CAAC;SACpE,WAAW,CAAC,mCAAmC,CAAC;IAEnD,OAAO,EAAE,KAAK,CAAC,IAAI,CAAC;SACjB,SAAS,EAAE;SACX,WAAW,CAAC,mBAAmB,CAAC;CACpC,CAAC;AAEF,kBAAe;IACb,aAAa,EAAb,qBAAa;IACb,kBAAkB,EAAlB,0BAAkB;IAClB,sBAAsB,EAAtB,8BAAsB;IACtB,iBAAiB,EAAjB,yBAAiB;IACjB,cAAc,EAAd,sBAAc;IACd,uBAAuB,EAAvB,+BAAuB;CACxB,CAAC"}