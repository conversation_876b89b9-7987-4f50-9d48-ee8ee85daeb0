# reCAPTCHA v3 Setup Guide

This guide will help you set up Google reCAPTCHA v3 for your InvoNest application to protect authentication forms from bots and abuse.

## 🔧 Setup Steps

### 1. Create reCAPTCHA v3 Site

1. Go to [Google reCAPTCHA Admin Console](https://www.google.com/recaptcha/admin)
2. Click "+" to create a new site
3. Fill in the form:
   - **Label**: InvoNest (or your preferred name)
   - **reCAPTCHA type**: Select "reCAPTCHA v3"
   - **Domains**: Add your domains:
     - `localhost` (for development)
     - `yourdomain.com` (for production)
   - **Accept the Terms of Service**
4. Click "Submit"

### 2. Get Your Keys

After creating the site, you'll get:
- **Site Key** (public key - used in frontend)
- **Secret Key** (private key - used in backend)

### 3. Configure Environment Variables

#### Frontend (frontend/.env)
```env
NEXT_PUBLIC_RECAPTCHA_SITE_KEY=your_site_key_here
```

#### Backend (backend/.env)
```env
RECAPTCHA_SECRET_KEY=your_secret_key_here
RECAPTCHA_MIN_SCORE=0.5
```

**Note**: The environment variables have already been added to your `.env` files. You just need to replace the placeholder values with your actual reCAPTCHA keys.

### 4. Install Dependencies

The required dependencies are already included in the project:
- Frontend: Uses native browser reCAPTCHA API
- Backend: Uses `node-fetch` for verification

## 🛡️ Security Configuration

### Score Threshold

reCAPTCHA v3 returns a score from 0.0 (likely bot) to 1.0 (likely human). The default threshold is 0.5:

- **0.9 - 1.0**: Very likely human
- **0.7 - 0.9**: Likely human  
- **0.3 - 0.7**: Neutral
- **0.0 - 0.3**: Likely bot

You can adjust the `RECAPTCHA_MIN_SCORE` environment variable:
- **0.3**: More lenient (fewer false positives)
- **0.5**: Balanced (recommended)
- **0.7**: Stricter (more security)

### Actions

The following actions are configured:
- `login`: User login attempts
- `register`: User registration
- `otp_login`: OTP login requests
- `otp_verify`: OTP verification
- `forgot_password`: Password reset requests

## 🔍 Testing

### Development Mode

In development, if `RECAPTCHA_SECRET_KEY` is not set, verification will be skipped with a warning.

### Production Testing

1. Test with different user behaviors:
   - Normal human interaction (should score > 0.5)
   - Rapid form submissions (may score lower)
   - Automated scripts (should score < 0.3)

2. Monitor the console logs for verification results:
   ```
   🔐 reCAPTCHA verification result: {
     success: true,
     score: 0.9,
     action: 'login'
   }
   ```

## 🚨 Troubleshooting

### Common Issues

1. **"reCAPTCHA not loaded yet"**
   - Wait for the script to load before submitting forms
   - Check network connectivity

2. **"Security verification failed"**
   - Verify your site key is correct
   - Check if domain is registered in reCAPTCHA console
   - Ensure score meets minimum threshold

3. **"Invalid security verification action"**
   - Action mismatch between frontend and backend
   - Check RECAPTCHA_ACTIONS constants

### Debug Mode

Enable debug logging by checking browser console and server logs for detailed reCAPTCHA verification information.

## 📊 Monitoring

### Analytics

Monitor reCAPTCHA performance in the [reCAPTCHA Admin Console](https://www.google.com/recaptcha/admin):
- Request volume
- Score distribution
- Action breakdown
- Error rates

### Alerts

Set up monitoring for:
- High bot traffic (many low scores)
- reCAPTCHA service errors
- Unusual score patterns

## 🔒 Best Practices

1. **Never expose secret key** in frontend code
2. **Use HTTPS** in production for secure token transmission
3. **Monitor scores** and adjust thresholds based on your traffic
4. **Implement fallbacks** for when reCAPTCHA service is unavailable
5. **Rate limit** endpoints even with reCAPTCHA protection
6. **Log verification results** for security analysis

## 🌐 Production Deployment

1. Update domains in reCAPTCHA console
2. Set production environment variables
3. Test thoroughly before going live
4. Monitor initial traffic for score patterns
5. Adjust thresholds if needed

## 📞 Support

For issues with reCAPTCHA setup:
1. Check [Google reCAPTCHA Documentation](https://developers.google.com/recaptcha/docs/v3)
2. Review server logs for detailed error messages
3. Test with different browsers and devices
4. Verify network connectivity to Google services

## 🚀 Quick Setup Summary

Since you're using `.env` files directly, here's what you need to do:

1. **Get reCAPTCHA Keys**: Visit [Google reCAPTCHA Admin](https://www.google.com/recaptcha/admin) and create a v3 site
2. **Update Environment Variables**:
   - In `frontend/.env`: Replace `your_recaptcha_site_key_here` with your actual site key
   - In `backend/.env`: Replace `your_recaptcha_secret_key_here` with your actual secret key
3. **Restart Both Servers**: Frontend and backend need to restart to pick up new environment variables
4. **Test**: Try logging in/registering to see reCAPTCHA in action

The reCAPTCHA implementation is already complete and ready to use once you add your keys!
