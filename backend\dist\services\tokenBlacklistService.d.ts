declare class TokenBlacklistService {
    private blacklistedTokens;
    private redisClient;
    constructor();
    private initializeRedis;
    /**
     * Blacklist a token
     */
    blacklistToken(token: string, reason?: 'logout' | 'security' | 'admin'): Promise<void>;
    /**
     * Check if a token is blacklisted
     */
    isTokenBlacklisted(token: string): Promise<boolean>;
    /**
     * Blacklist all tokens for a user (useful for security incidents)
     */
    blacklistAllUserTokens(userId: string, reason?: 'security' | 'admin'): Promise<void>;
    /**
     * Check if all user tokens are blacklisted
     */
    isUserBlacklisted(userId: string): Promise<boolean>;
    /**
     * Remove token from blacklist (rarely used)
     */
    removeFromBlacklist(token: string): Promise<void>;
    /**
     * Get blacklist statistics
     */
    getBlacklistStats(): Promise<{
        totalBlacklistedTokens: number;
        blacklistedUsers: number;
        recentBlacklists: any[];
    }>;
    /**
     * Clean up expired tokens from memory (only needed for in-memory storage)
     */
    private startCleanupInterval;
    /**
     * Graceful shutdown
     */
    shutdown(): Promise<void>;
}
export declare const tokenBlacklist: TokenBlacklistService;
export default TokenBlacklistService;
//# sourceMappingURL=tokenBlacklistService.d.ts.map