"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const notificationController_1 = require("../controllers/notificationController");
const auth_1 = require("../middleware/auth");
const notificationValidation_1 = require("../middleware/notificationValidation");
const router = express_1.default.Router();
// All routes require authentication
router.use(auth_1.authenticate);
// Get notifications
router.get('/', notificationController_1.getNotifications);
// Get unread notification count
router.get('/unread-count', notificationController_1.getUnreadCount);
// Mark notification as read
router.patch('/:notificationId/read', notificationController_1.markNotificationRead);
// Mark all notifications as read
router.patch('/mark-all-read', notificationController_1.markAllNotificationsRead);
// Delete notification
router.delete('/:notificationId', notificationController_1.deleteNotification);
// Create notification (admin/system use)
router.post('/', notificationValidation_1.validateNotificationCreation, notificationController_1.createNotification);
// Notification preferences
router.get('/preferences', notificationController_1.getNotificationPreferences);
router.put('/preferences', notificationValidation_1.validateNotificationPreferences, notificationController_1.updateNotificationPreferences);
exports.default = router;
//# sourceMappingURL=notificationRoutes.js.map