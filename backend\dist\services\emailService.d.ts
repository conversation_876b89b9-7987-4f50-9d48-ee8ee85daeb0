declare class EmailService {
    private transporter;
    private isInitializing;
    private initPromise;
    constructor();
    private getTransporter;
    sendEmail(to: string, subject: string, html: string, text?: string): Promise<boolean>;
    sendComplianceReminder(to: string, complianceTitle: string, dueDate: Date, daysLeft: number, penaltyInfo?: string): Promise<boolean>;
    sendInvoiceReminder(to: string, invoiceNumber: string, dueDate: Date, amount: number, customerName: string): Promise<boolean>;
    sendWelcomeEmail(to: string, userName: string): Promise<boolean>;
    sendPasswordResetEmail(to: string, resetToken: string): Promise<boolean>;
    sendEmailVerificationEmail(to: string, verificationToken: string, userName: string): Promise<boolean>;
    sendOTPEmail(to: string, otp: string, userName: string, purpose?: string): Promise<boolean>;
    sendLoginOTPEmail(to: string, otp: string, userName: string): Promise<boolean>;
    sendInvoiceEmail(to: string, invoiceData: {
        invoiceNumber: string;
        customerName: string;
        amount: number;
        dueDate?: Date;
        businessName: string;
        invoiceUrl: string;
        upiId?: string;
        bankDetails?: {
            accountNumber?: string;
            ifscCode?: string;
            bankName?: string;
            accountHolderName?: string;
        };
    }, pdfBuffer: Buffer): Promise<boolean>;
    sendReceiptEmail(to: string, receiptData: {
        receiptNumber: string;
        transactionId: string;
        customerName: string;
        amount: number;
        paidAt: Date;
        invoiceNumber: string;
        businessName: string;
    }, pdfBuffer: Buffer): Promise<boolean>;
    private getReceiptEmailTemplate;
    private getComplianceReminderTemplate;
    private getInvoiceReminderTemplate;
    private getWelcomeTemplate;
    private getPasswordResetTemplate;
    private htmlToText;
    private getEmailVerificationTemplate;
    private getOTPTemplate;
    private getLoginOTPTemplate;
    /**
     * Send payment reminder email
     */
    sendPaymentReminder(email: string, reminderData: {
        invoiceNumber: string;
        customerName: string;
        amount: number;
        dueDate: Date;
        type: 'upcoming' | 'due' | 'overdue';
        days: number;
        businessName: string;
        invoiceUrl: string;
    }): Promise<boolean>;
    /**
     * Generate payment reminder email template
     */
    private getPaymentReminderTemplate;
    private getInvoiceEmailTemplate;
    /**
     * Send payment confirmation email to customer
     */
    sendPaymentConfirmationEmail(to: string, paymentData: {
        paymentId: string;
        receiptNumber: string;
        amount: number;
        invoiceNumber: string;
        businessName: string;
        paymentMethod: string;
        transactionId?: string;
        paymentDate: Date;
    }): Promise<boolean>;
    /**
     * Send payment notification email to business owner
     */
    sendPaymentNotificationEmail(to: string, paymentData: {
        paymentId: string;
        receiptNumber: string;
        amount: number;
        invoiceNumber: string;
        customerName: string;
        paymentMethod: string;
        transactionId?: string;
        paymentDate: Date;
    }): Promise<boolean>;
    /**
     * Generate payment confirmation email template for customer
     */
    private getPaymentConfirmationTemplate;
    /**
     * Generate payment notification email template for business owner
     */
    private getPaymentNotificationTemplate;
    verifyConnection(): Promise<boolean>;
}
export { EmailService };
export declare const getEmailService: () => EmailService;
export default getEmailService;
//# sourceMappingURL=emailService.d.ts.map