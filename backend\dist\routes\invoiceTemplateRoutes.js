"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const invoiceTemplateController_1 = require("../controllers/invoiceTemplateController");
const auth_1 = require("../middleware/auth");
const router = express_1.default.Router();
// All routes require authentication
router.use(auth_1.authenticate);
// Template categories
router.get('/categories', invoiceTemplateController_1.getTemplateCategories);
// Template CRUD operations
router.get('/', invoiceTemplateController_1.getInvoiceTemplates);
router.get('/:id', invoiceTemplateController_1.getInvoiceTemplate);
router.post('/', invoiceTemplateController_1.createInvoiceTemplate);
router.put('/:id', invoiceTemplateController_1.updateInvoiceTemplate);
router.delete('/:id', invoiceTemplateController_1.deleteInvoiceTemplate);
// Special operations
router.post('/:id/create-invoice', invoiceTemplateController_1.createInvoiceFromTemplate);
router.patch('/:id/set-default', invoiceTemplateController_1.setDefaultTemplate);
exports.default = router;
//# sourceMappingURL=invoiceTemplateRoutes.js.map