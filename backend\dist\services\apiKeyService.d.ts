export interface CreateApiKeyData {
    userId: string;
    name: string;
    permissions?: {
        invoices?: {
            read?: boolean;
            create?: boolean;
            update?: boolean;
            delete?: boolean;
        };
        customers?: {
            read?: boolean;
            create?: boolean;
            update?: boolean;
            delete?: boolean;
        };
        documents?: {
            read?: boolean;
            create?: boolean;
        };
    };
    expiresAt?: Date;
    rateLimit?: {
        requestsPerMinute?: number;
        requestsPerHour?: number;
        requestsPerDay?: number;
    };
}
export declare class ApiKeyService {
    private redis?;
    constructor();
    /**
     * Create a new API key
     */
    createApiKey(data: CreateApiKeyData): Promise<{
        success: boolean;
        message: string;
        data?: any;
    }>;
    /**
     * Get user's API keys
     */
    getUserApiKeys(userId: string): Promise<any[]>;
    /**
     * Update API key
     */
    updateApiKey(userId: string, keyId: string, updates: Partial<CreateApiKeyData>): Promise<{
        success: boolean;
        message: string;
        data?: any;
    }>;
    /**
     * Delete API key
     */
    deleteApiKey(userId: string, keyId: string): Promise<{
        success: boolean;
        message: string;
    }>;
    /**
     * Verify API key and check permissions
     */
    verifyApiKey(apiKey: string): Promise<{
        valid: boolean;
        userId?: string;
        permissions?: any;
        keyId?: string;
    }>;
    /**
     * Check rate limit for API key
     */
    checkRateLimit(keyId: string, rateLimit: any): Promise<{
        allowed: boolean;
        remaining?: number;
    }>;
    /**
     * Get API key usage statistics
     */
    getUsageStats(userId: string, keyId?: string): Promise<any>;
}
export default ApiKeyService;
//# sourceMappingURL=apiKeyService.d.ts.map