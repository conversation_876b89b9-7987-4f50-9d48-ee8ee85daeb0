import { Response } from 'express';
/**
 * Get payment reminder settings for the user
 */
export declare const getReminderSettings: (req: any, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
/**
 * Update payment reminder settings
 */
export declare const updateReminderSettings: (req: any, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
/**
 * Send manual payment reminder for an invoice
 */
export declare const sendManualReminder: (req: any, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
/**
 * Get payment reminder statistics
 */
export declare const getReminderStats: (req: any, res: Response) => Promise<void>;
/**
 * Get upcoming payment reminders
 */
export declare const getUpcomingReminders: (req: any, res: Response) => Promise<void>;
/**
 * Get overdue invoices
 */
export declare const getOverdueInvoices: (req: any, res: Response) => Promise<void>;
//# sourceMappingURL=paymentReminderController.d.ts.map