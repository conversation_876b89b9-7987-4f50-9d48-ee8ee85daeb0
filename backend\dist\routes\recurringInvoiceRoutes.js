"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const recurringInvoiceController_1 = require("../controllers/recurringInvoiceController");
const auth_1 = require("../middleware/auth");
const router = express_1.default.Router();
// All routes require authentication
router.use(auth_1.authenticate);
// Recurring invoice CRUD operations
router.get('/', recurringInvoiceController_1.getRecurringInvoices);
router.get('/:id', recurringInvoiceController_1.getRecurringInvoice);
router.post('/', recurringInvoiceController_1.createRecurringInvoice);
router.put('/:id', recurringInvoiceController_1.updateRecurringInvoice);
router.delete('/:id', recurringInvoiceController_1.deleteRecurringInvoice);
// Special operations
router.patch('/:id/toggle', recurringInvoiceController_1.toggleRecurringInvoice);
router.post('/:id/generate', recurringInvoiceController_1.generateInvoiceNow);
exports.default = router;
//# sourceMappingURL=recurringInvoiceRoutes.js.map