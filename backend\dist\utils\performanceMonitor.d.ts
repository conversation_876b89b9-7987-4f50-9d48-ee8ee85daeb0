/**
 * Performance monitoring utility for tracking slow operations
 */
declare class PerformanceMonitor {
    private static instance;
    private metrics;
    private maxMetrics;
    static getInstance(): PerformanceMonitor;
    /**
     * Start timing an operation
     */
    startTimer(operation: string): (metadata?: any) => void;
    /**
     * Record a performance metric
     */
    private recordMetric;
    /**
     * Get performance statistics
     */
    getStats(operation?: string): any;
    /**
     * Get all operation types
     */
    getOperations(): string[];
    /**
     * Clear metrics
     */
    clear(): void;
}
export default PerformanceMonitor;
export declare const timeOperation: (operation: string) => (metadata?: any) => void;
//# sourceMappingURL=performanceMonitor.d.ts.map