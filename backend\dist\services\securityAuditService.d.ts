export interface SecurityAuditResult {
    timestamp: Date;
    overallScore: number;
    categories: {
        authentication: SecurityCategoryResult;
        authorization: SecurityCategoryResult;
        dataProtection: SecurityCategoryResult;
        inputValidation: SecurityCategoryResult;
        networkSecurity: SecurityCategoryResult;
        logging: SecurityCategoryResult;
        configuration: SecurityCategoryResult;
    };
    vulnerabilities: SecurityVulnerability[];
    recommendations: SecurityRecommendation[];
}
export interface SecurityCategoryResult {
    score: number;
    maxScore: number;
    issues: string[];
    passed: string[];
}
export interface SecurityVulnerability {
    id: string;
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    category: string;
    title: string;
    description: string;
    impact: string;
    remediation: string;
    cwe?: string;
    cvss?: number;
}
export interface SecurityRecommendation {
    priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    category: string;
    title: string;
    description: string;
    implementation: string;
    estimatedEffort: string;
}
declare class SecurityAuditService {
    private vulnerabilities;
    private recommendations;
    /**
     * Perform comprehensive security audit
     */
    performSecurityAudit(): Promise<SecurityAuditResult>;
    /**
     * Audit authentication mechanisms
     */
    private auditAuthentication;
    /**
     * Audit authorization mechanisms
     */
    private auditAuthorization;
    /**
     * Audit data protection measures
     */
    private auditDataProtection;
    private checkFileExists;
    private checkForSessionSecurity;
    private checkForRBAC;
    private checkForPrivilegeEscalationProtection;
    private checkForResourceLevelAuth;
    private checkForEncryptionAtRest;
    private checkForSecureBackups;
    private calculateOverallScore;
    private addVulnerability;
    private addRecommendation;
    private calculateCVSS;
    private auditInputValidation;
    private auditNetworkSecurity;
    private auditLogging;
    private auditConfiguration;
}
export declare const securityAudit: SecurityAuditService;
export default SecurityAuditService;
//# sourceMappingURL=securityAuditService.d.ts.map