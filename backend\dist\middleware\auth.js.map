{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../src/middleware/auth.ts"], "names": [], "mappings": ";;;;;;AACA,sCAAuD;AACvD,0DAA6C;AAC7C,6EAAmE;AACnE,qFAAgH;AAChH,kDAA0E;AAgBnE,MAAM,YAAY,GAAG,KAAK,EAC/B,GAAY,EACZ,GAAa,EACb,IAAkB,EACH,EAAE;IACjB,IAAI,CAAC;QACH,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC;QAE7C,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YACrD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,mCAAmC;aAC7C,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAED,MAAM,KAAK,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,0BAA0B;QAEjE,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,sCAAsC;aAChD,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAED,gCAAgC;QAChC,MAAM,aAAa,GAAG,MAAM,sCAAc,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;QACrE,IAAI,aAAa,EAAE,CAAC;YAClB,8CAAkB,CAAC,gBAAgB,CAAC;gBAClC,IAAI,EAAE,6CAAiB,CAAC,mBAAmB;gBAC3C,QAAQ,EAAE,4CAAgB,CAAC,MAAM;gBACjC,OAAO,EAAE,kCAAkC;gBAC3C,EAAE,EAAE,GAAG,CAAC,EAAE,IAAI,SAAS;gBACvB,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;gBAChC,IAAI,EAAE,GAAG,CAAC,IAAI;gBACd,MAAM,EAAE,GAAG,CAAC,MAAM;gBAClB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,QAAQ,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,EAAE;aACpD,CAAC,CAAC;YAEH,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,iDAAiD;aAC3D,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAED,eAAe;QACf,MAAM,OAAO,GAAe,IAAA,iBAAW,EAAC,KAAK,CAAC,CAAC;QAE/C,wCAAwC;QACxC,MAAM,iBAAiB,GAAG,MAAM,sCAAc,CAAC,iBAAiB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACjF,IAAI,iBAAiB,EAAE,CAAC;YACtB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,4DAA4D;aACtE,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAED,iCAAiC;QACjC,IAAI,IAAA,yBAAa,EAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YAClC,oBAAoB;YACpB,MAAM,SAAS,GAAG,IAAA,iCAAqB,GAAE,CAAC;YAC1C,GAAG,CAAC,IAAI,GAAG,SAAgB,CAAC;QAC9B,CAAC;aAAM,CAAC;YACN,iCAAiC;YACjC,MAAM,IAAI,GAAG,MAAM,cAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;YAErE,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,oCAAoC;iBAC9C,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACnB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,iDAAiD;iBAC3D,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,gCAAgC;YAChC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;QAClB,CAAC;QACD,IAAI,EAAE,CAAC;IAET,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;QAC9C,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,2BAA2B;SACrC,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC;AAlGW,QAAA,YAAY,gBAkGvB;AAEK,MAAM,SAAS,GAAG,CAAC,GAAG,KAAe,EAAE,EAAE;IAC9C,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;QAC/D,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YACd,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,0BAA0B;aACpC,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YACnC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,0CAA0C;aACpD,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AApBW,QAAA,SAAS,aAoBpB;AAEK,MAAM,YAAY,GAAG,KAAK,EAC/B,GAAY,EACZ,GAAa,EACb,IAAkB,EACH,EAAE;IACjB,IAAI,CAAC;QACH,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC;QAE7C,IAAI,UAAU,IAAI,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YACnD,MAAM,KAAK,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAEtC,IAAI,KAAK,EAAE,CAAC;gBACV,MAAM,OAAO,GAAe,IAAA,iBAAW,EAAC,KAAK,CAAC,CAAC;gBAE/C,IAAI,IAAA,yBAAa,EAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;oBAClC,oBAAoB;oBACpB,MAAM,SAAS,GAAG,IAAA,iCAAqB,GAAE,CAAC;oBAC1C,GAAG,CAAC,IAAI,GAAG,SAAgB,CAAC;gBAC9B,CAAC;qBAAM,CAAC;oBACN,sBAAsB;oBACtB,MAAM,IAAI,GAAG,MAAM,cAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;oBAErE,IAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;wBAC1B,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;oBAClB,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,uEAAuE;QACvE,IAAI,EAAE,CAAC;IACT,CAAC;AACH,CAAC,CAAC;AAlCW,QAAA,YAAY,gBAkCvB"}