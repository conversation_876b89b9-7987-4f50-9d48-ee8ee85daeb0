"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const auth_1 = require("../middleware/auth");
const User_1 = __importDefault(require("../models/User"));
const Invoice_1 = __importDefault(require("../models/Invoice"));
const router = express_1.default.Router();
// All admin routes require authentication and admin role
router.use(auth_1.authenticate);
router.use((0, auth_1.authorize)('admin'));
// Get admin dashboard stats
router.get('/dashboard/stats', async (req, res) => {
    try {
        // Get basic statistics
        const [totalUsers, totalInvoices, activeUsers, recentUsers] = await Promise.all([
            User_1.default.countDocuments(),
            Invoice_1.default.countDocuments(),
            User_1.default.countDocuments({ isActive: true }),
            User_1.default.find({ isActive: true })
                .sort({ createdAt: -1 })
                .limit(10)
                .select('name email businessName createdAt lastLogin')
        ]);
        // Calculate revenue (if you have subscription data)
        const totalRevenue = 0; // Placeholder - implement based on your subscription model
        res.status(200).json({
            success: true,
            message: 'Admin dashboard stats retrieved successfully',
            data: {
                stats: {
                    totalUsers,
                    totalInvoices,
                    activeUsers,
                    totalRevenue
                },
                recentUsers
            }
        });
    }
    catch (error) {
        console.error('Admin dashboard stats error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});
// Get all users with pagination
router.get('/users', async (req, res) => {
    try {
        const { page = 1, limit = 20, search, status } = req.query;
        const pageNum = parseInt(page);
        const limitNum = parseInt(limit);
        const skip = (pageNum - 1) * limitNum;
        // Build query
        const query = {};
        if (search) {
            query.$or = [
                { name: { $regex: search, $options: 'i' } },
                { email: { $regex: search, $options: 'i' } },
                { businessName: { $regex: search, $options: 'i' } }
            ];
        }
        if (status === 'active') {
            query.isActive = true;
        }
        else if (status === 'inactive') {
            query.isActive = false;
        }
        const [users, total] = await Promise.all([
            User_1.default.find(query)
                .select('-password')
                .sort({ createdAt: -1 })
                .skip(skip)
                .limit(limitNum),
            User_1.default.countDocuments(query)
        ]);
        res.status(200).json({
            success: true,
            message: 'Users retrieved successfully',
            data: {
                users,
                pagination: {
                    current: pageNum,
                    pages: Math.ceil(total / limitNum),
                    total,
                    limit: limitNum
                }
            }
        });
    }
    catch (error) {
        console.error('Admin get users error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});
// Toggle user active status
router.patch('/users/:userId/toggle-status', async (req, res) => {
    try {
        const { userId } = req.params;
        const user = await User_1.default.findById(userId);
        if (!user) {
            res.status(404).json({
                success: false,
                message: 'User not found'
            });
            return;
        }
        user.isActive = !user.isActive;
        await user.save();
        res.status(200).json({
            success: true,
            message: `User ${user.isActive ? 'activated' : 'deactivated'} successfully`,
            data: {
                userId: user._id,
                isActive: user.isActive
            }
        });
    }
    catch (error) {
        console.error('Admin toggle user status error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});
exports.default = router;
//# sourceMappingURL=adminRoutes.js.map