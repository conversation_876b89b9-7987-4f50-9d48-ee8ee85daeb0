interface OTPValidationResult {
    isValid: boolean;
    message: string;
    remainingAttempts?: number;
}
declare class OTPService {
    private otpStore;
    private readonly OTP_LENGTH;
    private readonly OTP_EXPIRY_MINUTES;
    private readonly MAX_ATTEMPTS;
    private readonly CLEANUP_INTERVAL;
    constructor();
    /**
     * Generate a secure 6-digit OTP
     */
    private generateOTP;
    /**
     * Generate OTP key for storage
     */
    private generateOTPKey;
    /**
     * Send OTP for various purposes
     */
    sendOTP(email: string, userName: string, purpose: 'login' | 'registration' | 'password-reset' | 'profile-update' | 'sensitive-operation'): Promise<{
        success: boolean;
        message: string;
        otpId?: string;
    }>;
    /**
     * Verify OTP
     */
    verifyOTP(email: string, otp: string, purpose: string): Promise<OTPValidationResult>;
    /**
     * Check if OTP is expired
     */
    private isExpired;
    /**
     * Clean up expired OTPs
     */
    private cleanupExpiredOTPs;
    /**
     * Get OTP statistics (for monitoring)
     */
    getStats(): {
        totalActive: number;
        byPurpose: Record<string, number>;
        oldestOTP: Date | null;
    };
    /**
     * Invalidate all OTPs for a user (useful for security purposes)
     */
    invalidateUserOTPs(email: string): number;
}
declare const otpService: OTPService;
export default otpService;
//# sourceMappingURL=otpService.d.ts.map