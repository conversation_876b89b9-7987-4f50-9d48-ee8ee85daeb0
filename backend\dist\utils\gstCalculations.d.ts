import { IInvoiceItem } from '../models/Invoice';
export declare const INDIAN_STATES: {
    'Andhra Pradesh': string;
    'Arunachal Pradesh': string;
    Assam: string;
    Bihar: string;
    Chhattisgarh: string;
    Goa: string;
    Gujarat: string;
    Haryana: string;
    'Himachal Pradesh': string;
    Jharkhand: string;
    Karnataka: string;
    Kerala: string;
    'Madhya Pradesh': string;
    Maharashtra: string;
    Manipur: string;
    Meghalaya: string;
    Mizoram: string;
    Nagaland: string;
    Odisha: string;
    Punjab: string;
    Rajasthan: string;
    Sikkim: string;
    'Tamil Nadu': string;
    Telangana: string;
    Tripura: string;
    'Uttar Pradesh': string;
    Uttarakhand: string;
    'West Bengal': string;
    Delhi: string;
    'Jammu and Kashmir': string;
    Ladakh: string;
    Chandigarh: string;
    'Dadra and Nagar Haveli and Daman and Diu': string;
    Lakshadweep: string;
    Puducherry: string;
    'Andaman and Nicobar Islands': string;
};
export interface GSTRates {
    cgst: number;
    sgst: number;
    igst: number;
}
export interface ItemCalculation {
    description: string;
    hsn: string;
    quantity: number;
    unit: string;
    rate: number;
    discount: number;
    taxableAmount: number;
    gstRates: GSTRates;
    cgstAmount: number;
    sgstAmount: number;
    igstAmount: number;
    totalAmount: number;
}
export interface InvoiceCalculation {
    items: ItemCalculation[];
    subtotal: number;
    totalDiscount: number;
    taxableAmount: number;
    totalCGST: number;
    totalSGST: number;
    totalIGST: number;
    totalTax: number;
    grandTotal: number;
}
/**
 * Determine if transaction is inter-state or intra-state
 * @param sellerState - Seller's state
 * @param buyerState - Buyer's state
 * @returns true if inter-state (IGST applicable), false if intra-state (CGST+SGST applicable)
 */
export declare const isInterStateTransaction: (sellerState: string, buyerState: string) => boolean;
/**
 * Get GST rates based on HSN code and transaction type
 * @param hsnCode - HSN/SAC code
 * @param isInterState - Whether it's inter-state transaction
 * @returns GST rates object
 */
export declare const getGSTRates: (hsnCode: string, isInterState: boolean) => GSTRates;
/**
 * Calculate item-wise GST amounts
 * @param item - Invoice item data
 * @param sellerState - Seller's state
 * @param buyerState - Buyer's state
 * @returns Calculated item with GST amounts
 */
export declare const calculateItemGST: (item: {
    description: string;
    hsn: string;
    quantity: number;
    unit: string;
    rate: number;
    discount?: number;
}, sellerState: string, buyerState: string) => ItemCalculation;
/**
 * Calculate simple invoice without GST (for non-GST registered businesses)
 * @param items - Array of invoice items
 * @returns Simple invoice calculation without GST
 */
export declare const calculateInvoiceSimple: (items: Array<{
    description: string;
    hsn?: string;
    quantity: number;
    unit: string;
    rate: number;
    discount?: number;
}>) => InvoiceCalculation;
/**
 * Calculate complete invoice with GST
 * @param items - Array of invoice items
 * @param sellerState - Seller's state
 * @param buyerState - Buyer's state
 * @returns Complete invoice calculation
 */
export declare const calculateInvoiceGST: (items: Array<{
    description: string;
    hsn: string;
    quantity: number;
    unit: string;
    rate: number;
    discount?: number;
}>, sellerState: string, buyerState: string) => InvoiceCalculation;
/**
 * Convert invoice items to database format
 * @param calculatedItems - Calculated items from calculateInvoiceGST
 * @returns Array of IInvoiceItem for database storage
 */
export declare const convertToInvoiceItems: (calculatedItems: ItemCalculation[]) => IInvoiceItem[];
/**
 * Validate GST number format
 * @param gstNumber - GST number to validate
 * @returns true if valid, false otherwise
 */
export declare const validateGSTNumber: (gstNumber: string) => boolean;
/**
 * Get state code from GST number
 * @param gstNumber - Valid GST number
 * @returns State code (first 2 digits)
 */
export declare const getStateCodeFromGST: (gstNumber: string) => string;
//# sourceMappingURL=gstCalculations.d.ts.map