import { Request, Response, NextFunction } from 'express';
/**
 * Sanitize string input by removing dangerous characters
 */
export declare const sanitizeString: (input: string) => string;
/**
 * Detect dangerous patterns in input
 */
export declare const detectDangerousPatterns: (input: string) => {
    detected: boolean;
    type: string;
    pattern?: string;
};
/**
 * Comprehensive input sanitization middleware
 */
export declare const sanitizeInput: (req: Request, res: Response, next: NextFunction) => void;
/**
 * File upload validation middleware
 */
export declare const validateFileUpload: (allowedTypes?: "images" | "documents" | "all") => (req: Request, res: Response, next: NextFunction) => void;
/**
 * Enhanced validation result handler
 */
export declare const handleValidationErrors: (req: Request, res: Response, next: NextFunction) => void;
export declare const commonValidations: {
    email: {
        isEmail: () => /*elided*/ any;
        normalizeEmail: () => /*elided*/ any;
        withMessage: (msg?: any) => /*elided*/ any;
        isLength: (options?: any) => /*elided*/ any;
        matches: (pattern?: any) => /*elided*/ any;
        trim: () => /*elided*/ any;
        optional: () => /*elided*/ any;
        isMongoId: () => /*elided*/ any;
    };
    password: {
        isEmail: () => /*elided*/ any;
        normalizeEmail: () => /*elided*/ any;
        withMessage: (msg?: any) => /*elided*/ any;
        isLength: (options?: any) => /*elided*/ any;
        matches: (pattern?: any) => /*elided*/ any;
        trim: () => /*elided*/ any;
        optional: () => /*elided*/ any;
        isMongoId: () => /*elided*/ any;
    };
    name: {
        isEmail: () => /*elided*/ any;
        normalizeEmail: () => /*elided*/ any;
        withMessage: (msg?: any) => /*elided*/ any;
        isLength: (options?: any) => /*elided*/ any;
        matches: (pattern?: any) => /*elided*/ any;
        trim: () => /*elided*/ any;
        optional: () => /*elided*/ any;
        isMongoId: () => /*elided*/ any;
    };
    phone: {
        isEmail: () => /*elided*/ any;
        normalizeEmail: () => /*elided*/ any;
        withMessage: (msg?: any) => /*elided*/ any;
        isLength: (options?: any) => /*elided*/ any;
        matches: (pattern?: any) => /*elided*/ any;
        trim: () => /*elided*/ any;
        optional: () => /*elided*/ any;
        isMongoId: () => /*elided*/ any;
    };
    gstNumber: {
        isEmail: () => /*elided*/ any;
        normalizeEmail: () => /*elided*/ any;
        withMessage: (msg?: any) => /*elided*/ any;
        isLength: (options?: any) => /*elided*/ any;
        matches: (pattern?: any) => /*elided*/ any;
        trim: () => /*elided*/ any;
        optional: () => /*elided*/ any;
        isMongoId: () => /*elided*/ any;
    };
    mongoId: {
        isEmail: () => /*elided*/ any;
        normalizeEmail: () => /*elided*/ any;
        withMessage: (msg?: any) => /*elided*/ any;
        isLength: (options?: any) => /*elided*/ any;
        matches: (pattern?: any) => /*elided*/ any;
        trim: () => /*elided*/ any;
        optional: () => /*elided*/ any;
        isMongoId: () => /*elided*/ any;
    };
};
declare const _default: {
    sanitizeInput: (req: Request, res: Response, next: NextFunction) => void;
    validateFileUpload: (allowedTypes?: "images" | "documents" | "all") => (req: Request, res: Response, next: NextFunction) => void;
    handleValidationErrors: (req: Request, res: Response, next: NextFunction) => void;
    commonValidations: {
        email: {
            isEmail: () => /*elided*/ any;
            normalizeEmail: () => /*elided*/ any;
            withMessage: (msg?: any) => /*elided*/ any;
            isLength: (options?: any) => /*elided*/ any;
            matches: (pattern?: any) => /*elided*/ any;
            trim: () => /*elided*/ any;
            optional: () => /*elided*/ any;
            isMongoId: () => /*elided*/ any;
        };
        password: {
            isEmail: () => /*elided*/ any;
            normalizeEmail: () => /*elided*/ any;
            withMessage: (msg?: any) => /*elided*/ any;
            isLength: (options?: any) => /*elided*/ any;
            matches: (pattern?: any) => /*elided*/ any;
            trim: () => /*elided*/ any;
            optional: () => /*elided*/ any;
            isMongoId: () => /*elided*/ any;
        };
        name: {
            isEmail: () => /*elided*/ any;
            normalizeEmail: () => /*elided*/ any;
            withMessage: (msg?: any) => /*elided*/ any;
            isLength: (options?: any) => /*elided*/ any;
            matches: (pattern?: any) => /*elided*/ any;
            trim: () => /*elided*/ any;
            optional: () => /*elided*/ any;
            isMongoId: () => /*elided*/ any;
        };
        phone: {
            isEmail: () => /*elided*/ any;
            normalizeEmail: () => /*elided*/ any;
            withMessage: (msg?: any) => /*elided*/ any;
            isLength: (options?: any) => /*elided*/ any;
            matches: (pattern?: any) => /*elided*/ any;
            trim: () => /*elided*/ any;
            optional: () => /*elided*/ any;
            isMongoId: () => /*elided*/ any;
        };
        gstNumber: {
            isEmail: () => /*elided*/ any;
            normalizeEmail: () => /*elided*/ any;
            withMessage: (msg?: any) => /*elided*/ any;
            isLength: (options?: any) => /*elided*/ any;
            matches: (pattern?: any) => /*elided*/ any;
            trim: () => /*elided*/ any;
            optional: () => /*elided*/ any;
            isMongoId: () => /*elided*/ any;
        };
        mongoId: {
            isEmail: () => /*elided*/ any;
            normalizeEmail: () => /*elided*/ any;
            withMessage: (msg?: any) => /*elided*/ any;
            isLength: (options?: any) => /*elided*/ any;
            matches: (pattern?: any) => /*elided*/ any;
            trim: () => /*elided*/ any;
            optional: () => /*elided*/ any;
            isMongoId: () => /*elided*/ any;
        };
    };
    sanitizeString: (input: string) => string;
    detectDangerousPatterns: (input: string) => {
        detected: boolean;
        type: string;
        pattern?: string;
    };
};
export default _default;
//# sourceMappingURL=inputSanitization.d.ts.map