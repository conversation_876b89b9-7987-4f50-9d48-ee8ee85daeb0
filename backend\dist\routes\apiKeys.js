"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const auth_1 = require("../middleware/auth");
const apiAuth_1 = require("../middleware/apiAuth");
const usageTracking_1 = require("../middleware/usageTracking");
const apiKeyController_1 = require("../controllers/apiKeyController");
const router = express_1.default.Router();
/**
 * @route   GET /api/api-keys/docs
 * @desc    Get API documentation
 * @access  Public
 */
router.get('/docs', apiKeyController_1.getApiDocs);
/**
 * @route   GET /api/api-keys/test
 * @desc    Test API key
 * @access  Private (API Key)
 */
router.get('/test', apiAuth_1.authenticateApiKey, apiKeyController_1.testApiKey);
// All routes below require JWT authentication and Business plan
router.use(auth_1.authenticate);
router.use((0, usageTracking_1.checkFeatureAccess)('apiAccess'));
/**
 * @route   POST /api/api-keys
 * @desc    Create a new API key
 * @access  Private (Business plan)
 */
router.post('/', apiKeyController_1.createApiKey);
/**
 * @route   GET /api/api-keys
 * @desc    Get user's API keys
 * @access  Private (Business plan)
 */
router.get('/', apiKeyController_1.getApiKeys);
/**
 * @route   PUT /api/api-keys/:keyId
 * @desc    Update API key
 * @access  Private (Business plan)
 */
router.put('/:keyId', apiKeyController_1.updateApiKey);
/**
 * @route   DELETE /api/api-keys/:keyId
 * @desc    Delete API key
 * @access  Private (Business plan)
 */
router.delete('/:keyId', apiKeyController_1.deleteApiKey);
/**
 * @route   GET /api/api-keys/usage
 * @desc    Get API key usage statistics
 * @access  Private (Business plan)
 */
router.get('/usage', apiKeyController_1.getUsageStats);
exports.default = router;
//# sourceMappingURL=apiKeys.js.map