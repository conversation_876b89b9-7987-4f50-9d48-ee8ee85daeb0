"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importStar(require("mongoose"));
const TeamMemberSchema = new mongoose_1.Schema({
    organizationId: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User',
        required: true,
        index: true
    },
    userId: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User',
        required: true,
        index: true
    },
    email: {
        type: String,
        required: true,
        lowercase: true,
        trim: true
    },
    role: {
        type: String,
        enum: ['owner', 'admin', 'manager', 'user'],
        required: true,
        default: 'user'
    },
    permissions: {
        invoices: {
            create: { type: Boolean, default: true },
            read: { type: Boolean, default: true },
            update: { type: Boolean, default: true },
            delete: { type: Boolean, default: false }
        },
        customers: {
            create: { type: Boolean, default: true },
            read: { type: Boolean, default: true },
            update: { type: Boolean, default: true },
            delete: { type: Boolean, default: false }
        },
        documents: {
            create: { type: Boolean, default: true },
            read: { type: Boolean, default: true },
            update: { type: Boolean, default: true },
            delete: { type: Boolean, default: false }
        },
        analytics: {
            read: { type: Boolean, default: false }
        },
        settings: {
            read: { type: Boolean, default: false },
            update: { type: Boolean, default: false }
        },
        team: {
            invite: { type: Boolean, default: false },
            manage: { type: Boolean, default: false }
        }
    },
    status: {
        type: String,
        enum: ['active', 'inactive', 'pending'],
        default: 'pending'
    },
    invitedBy: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    invitedAt: {
        type: Date,
        default: Date.now
    },
    joinedAt: {
        type: Date
    },
    lastActive: {
        type: Date
    },
    invitationToken: {
        type: String,
        select: false // Don't include in queries by default
    }
}, {
    timestamps: true
});
// Indexes for better query performance
TeamMemberSchema.index({ organizationId: 1, email: 1 }, { unique: true });
TeamMemberSchema.index({ organizationId: 1, status: 1 });
TeamMemberSchema.index({ userId: 1 });
// Pre-save middleware to set default permissions based on role
TeamMemberSchema.pre('save', function (next) {
    if (this.isModified('role')) {
        switch (this.role) {
            case 'owner':
                this.permissions = {
                    invoices: { create: true, read: true, update: true, delete: true },
                    customers: { create: true, read: true, update: true, delete: true },
                    documents: { create: true, read: true, update: true, delete: true },
                    analytics: { read: true },
                    settings: { read: true, update: true },
                    team: { invite: true, manage: true }
                };
                break;
            case 'admin':
                this.permissions = {
                    invoices: { create: true, read: true, update: true, delete: true },
                    customers: { create: true, read: true, update: true, delete: true },
                    documents: { create: true, read: true, update: true, delete: true },
                    analytics: { read: true },
                    settings: { read: true, update: false },
                    team: { invite: true, manage: true }
                };
                break;
            case 'manager':
                this.permissions = {
                    invoices: { create: true, read: true, update: true, delete: false },
                    customers: { create: true, read: true, update: true, delete: false },
                    documents: { create: true, read: true, update: true, delete: false },
                    analytics: { read: true },
                    settings: { read: true, update: false },
                    team: { invite: false, manage: false }
                };
                break;
            case 'user':
                this.permissions = {
                    invoices: { create: true, read: true, update: true, delete: false },
                    customers: { create: true, read: true, update: true, delete: false },
                    documents: { create: true, read: true, update: false, delete: false },
                    analytics: { read: false },
                    settings: { read: false, update: false },
                    team: { invite: false, manage: false }
                };
                break;
        }
    }
    next();
});
// Static method to get role hierarchy
TeamMemberSchema.statics.getRoleHierarchy = function () {
    return {
        owner: 4,
        admin: 3,
        manager: 2,
        user: 1
    };
};
// Instance method to check if user can perform action
TeamMemberSchema.methods.canPerform = function (resource, action) {
    const permissions = this.permissions[resource];
    return permissions && permissions[action] === true;
};
// Instance method to check if user can manage another user
TeamMemberSchema.methods.canManage = function (targetRole) {
    const hierarchy = this.constructor.getRoleHierarchy();
    return hierarchy[this.role] > hierarchy[targetRole];
};
exports.default = mongoose_1.default.model('TeamMember', TeamMemberSchema);
//# sourceMappingURL=TeamMember.js.map