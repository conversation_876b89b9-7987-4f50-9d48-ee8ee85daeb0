import mongoose, { Document, Model } from 'mongoose';
export interface IPaymentToken extends Document {
    tokenId: string;
    invoiceId: mongoose.Types.ObjectId;
    userId: mongoose.Types.ObjectId;
    amount: number;
    currency: string;
    isUsed: boolean;
    usedAt?: Date;
    expiresAt: Date;
    scanCount: number;
    lastScannedAt?: Date;
    lastScannedIP?: string;
    paymentId?: string;
    transactionId?: string;
    createdAt: Date;
    updatedAt: Date;
    markAsUsed(paymentId: string, transactionId?: string): Promise<void>;
    trackScan(ip?: string): Promise<void>;
    isValid(): boolean;
}
export interface IPaymentTokenModel extends Model<IPaymentToken> {
    generatePaymentToken(invoiceId: string, userId: string, amount: number, expiryHours?: number): Promise<IPaymentToken>;
}
declare const _default: IPaymentTokenModel;
export default _default;
//# sourceMappingURL=PaymentToken.d.ts.map