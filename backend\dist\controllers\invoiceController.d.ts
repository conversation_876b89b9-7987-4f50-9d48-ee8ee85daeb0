import { Request, Response } from 'express';
export declare const createInvoice: (req: Request, res: Response) => Promise<void>;
export declare const getInvoices: (req: Request, res: Response) => Promise<void>;
export declare const getInvoice: (req: Request, res: Response) => Promise<void>;
export declare const updateInvoice: (req: Request, res: Response) => Promise<void>;
export declare const deleteInvoice: (req: Request, res: Response) => Promise<void>;
export declare const downloadInvoicePDF: (req: Request, res: Response) => Promise<void>;
export declare const sendInvoiceEmail: (req: Request, res: Response) => Promise<void>;
export declare const getEmailQueueStatus: (req: Request, res: Response) => Promise<void>;
export declare const getPerformanceStats: (req: Request, res: Response) => Promise<void>;
export declare const verifyInvoiceIntegrity: (req: Request, res: Response) => Promise<void>;
//# sourceMappingURL=invoiceController.d.ts.map