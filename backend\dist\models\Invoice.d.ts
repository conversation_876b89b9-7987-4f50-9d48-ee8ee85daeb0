import mongoose, { Document } from 'mongoose';
export interface IInvoiceItem {
    description: string;
    hsn: string;
    quantity: number;
    unit: string;
    rate: number;
    discount?: number;
    taxableAmount: number;
    cgstRate: number;
    sgstRate: number;
    igstRate: number;
    cgstAmount: number;
    sgstAmount: number;
    igstAmount: number;
    totalAmount: number;
}
export interface ICustomer {
    name: string;
    email?: string;
    phone?: string;
    gstNumber?: string;
    address: {
        street: string;
        city: string;
        state: string;
        pincode: string;
        country: string;
    };
}
export interface IInvoice extends Document {
    invoiceNumber: string;
    invoiceDate: Date;
    dueDate?: Date;
    userId: mongoose.Types.ObjectId;
    invoiceType: 'gst' | 'non-gst';
    customer: ICustomer;
    items: IInvoiceItem[];
    subtotal: number;
    totalDiscount: number;
    taxableAmount: number;
    totalCGST: number;
    totalSGST: number;
    totalIGST: number;
    totalTax: number;
    grandTotal: number;
    notes?: string;
    terms?: string;
    status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';
    paymentStatus: 'pending' | 'partial' | 'paid';
    paymentDate?: Date;
    hash: string;
    remindersSent?: Array<{
        type: 'upcoming' | 'due' | 'overdue';
        sentAt: Date;
        days: number;
    }>;
    createdAt: Date;
    updatedAt: Date;
    generateHash(): string;
    verifyIntegrity(): boolean;
}
declare const _default: mongoose.Model<IInvoice, {}, {}, {}, mongoose.Document<unknown, {}, IInvoice, {}> & IInvoice & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
export default _default;
//# sourceMappingURL=Invoice.d.ts.map