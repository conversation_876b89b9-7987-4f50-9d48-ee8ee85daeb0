"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SECURITY_CONSTANTS = exports.PasswordValidator = exports.EncryptionService = exports.getSecurityConfig = void 0;
const crypto_1 = __importDefault(require("crypto"));
// Security configuration based on environment
const getSecurityConfig = () => {
    const isProduction = process.env.NODE_ENV === 'production';
    return {
        jwt: {
            secret: process.env.JWT_SECRET || 'fallback-secret-change-in-production',
            expiresIn: process.env.JWT_EXPIRE || '7d',
            refreshExpiresIn: '30d',
            algorithm: 'HS256'
        },
        encryption: {
            algorithm: 'aes-256-gcm',
            keyLength: 32,
            ivLength: 16
        },
        password: {
            saltRounds: parseInt(process.env.BCRYPT_ROUNDS || '12'),
            minLength: 8,
            requireUppercase: true,
            requireLowercase: true,
            requireNumbers: true,
            requireSpecialChars: true
        },
        rateLimit: {
            windowMs: 15 * 60 * 1000, // 15 minutes
            maxRequests: parseInt(process.env.RATE_LIMIT_MAX || '100'),
            authMaxRequests: 5,
            otpMaxRequests: 3
        },
        session: {
            maxAge: 24 * 60 * 60 * 1000, // 24 hours
            secure: isProduction,
            httpOnly: true,
            sameSite: isProduction ? 'strict' : 'lax'
        },
        cors: {
            origin: process.env.FRONTEND_URL || 'http://localhost:3000',
            credentials: true,
            methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
            allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
        },
        upload: {
            maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '10485760'), // 10MB
            allowedMimeTypes: [
                'application/pdf',
                'image/jpeg',
                'image/png',
                'image/gif',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            ],
            maxFiles: 5
        },
        api: {
            maxRequestSize: '10mb',
            timeout: 30000 // 30 seconds
        }
    };
};
exports.getSecurityConfig = getSecurityConfig;
// Encryption utilities
class EncryptionService {
    /**
     * Generate a secure encryption key
     */
    static generateKey() {
        return crypto_1.default.randomBytes(this.keyLength).toString('hex');
    }
    /**
     * Encrypt sensitive data
     */
    static encrypt(text, key) {
        const encryptionKey = key ? Buffer.from(key, 'hex') : crypto_1.default.randomBytes(this.keyLength);
        const iv = crypto_1.default.randomBytes(this.ivLength);
        const cipher = crypto_1.default.createCipheriv(this.algorithm, encryptionKey, iv);
        cipher.setAAD(Buffer.from('invonest-aad'));
        let encrypted = cipher.update(text, 'utf8', 'hex');
        encrypted += cipher.final('hex');
        const tag = cipher.getAuthTag();
        return {
            encrypted,
            iv: iv.toString('hex'),
            tag: tag.toString('hex')
        };
    }
    /**
     * Decrypt sensitive data
     */
    static decrypt(encryptedData, key) {
        const encryptionKey = Buffer.from(key, 'hex');
        const iv = Buffer.from(encryptedData.iv, 'hex');
        const tag = Buffer.from(encryptedData.tag, 'hex');
        const decipher = crypto_1.default.createDecipheriv(this.algorithm, encryptionKey, iv);
        decipher.setAAD(Buffer.from('invonest-aad'));
        decipher.setAuthTag(tag);
        let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
        decrypted += decipher.final('utf8');
        return decrypted;
    }
    /**
     * Hash sensitive data (one-way)
     */
    static hash(data) {
        return crypto_1.default.createHash('sha256').update(data).digest('hex');
    }
    /**
     * Generate secure random token
     */
    static generateToken(length = 32) {
        return crypto_1.default.randomBytes(length).toString('hex');
    }
    /**
     * Generate secure API key
     */
    static generateApiKey() {
        const prefix = 'sk_';
        const randomPart = crypto_1.default.randomBytes(24).toString('base64url');
        return prefix + randomPart;
    }
}
exports.EncryptionService = EncryptionService;
EncryptionService.algorithm = 'aes-256-gcm';
EncryptionService.keyLength = 32;
EncryptionService.ivLength = 16;
EncryptionService.tagLength = 16;
// Password validation utility
class PasswordValidator {
    /**
     * Validate password strength
     */
    static validate(password) {
        const errors = [];
        if (password.length < this.config.minLength) {
            errors.push(`Password must be at least ${this.config.minLength} characters long`);
        }
        if (this.config.requireUppercase && !/[A-Z]/.test(password)) {
            errors.push('Password must contain at least one uppercase letter');
        }
        if (this.config.requireLowercase && !/[a-z]/.test(password)) {
            errors.push('Password must contain at least one lowercase letter');
        }
        if (this.config.requireNumbers && !/\d/.test(password)) {
            errors.push('Password must contain at least one number');
        }
        if (this.config.requireSpecialChars && !/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
            errors.push('Password must contain at least one special character');
        }
        return {
            isValid: errors.length === 0,
            errors
        };
    }
    /**
     * Generate secure password
     */
    static generate(length = 16) {
        const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        const lowercase = 'abcdefghijklmnopqrstuvwxyz';
        const numbers = '0123456789';
        const special = '!@#$%^&*()_+-=[]{}|;:,.<>?';
        const allChars = uppercase + lowercase + numbers + special;
        let password = '';
        // Ensure at least one character from each required category
        password += uppercase[Math.floor(Math.random() * uppercase.length)];
        password += lowercase[Math.floor(Math.random() * lowercase.length)];
        password += numbers[Math.floor(Math.random() * numbers.length)];
        password += special[Math.floor(Math.random() * special.length)];
        // Fill the rest randomly
        for (let i = 4; i < length; i++) {
            password += allChars[Math.floor(Math.random() * allChars.length)];
        }
        // Shuffle the password
        return password.split('').sort(() => Math.random() - 0.5).join('');
    }
}
exports.PasswordValidator = PasswordValidator;
PasswordValidator.config = (0, exports.getSecurityConfig)().password;
// Security constants
exports.SECURITY_CONSTANTS = {
    MAX_LOGIN_ATTEMPTS: 5,
    LOCKOUT_DURATION: 30 * 60 * 1000, // 30 minutes
    OTP_EXPIRY: 10 * 60 * 1000, // 10 minutes
    SESSION_TIMEOUT: 24 * 60 * 60 * 1000, // 24 hours
    API_KEY_EXPIRY: 365 * 24 * 60 * 60 * 1000, // 1 year
    REFRESH_TOKEN_EXPIRY: 30 * 24 * 60 * 60 * 1000, // 30 days
    PASSWORD_RESET_EXPIRY: 15 * 60 * 1000, // 15 minutes
    EMAIL_VERIFICATION_EXPIRY: 24 * 60 * 60 * 1000, // 24 hours
};
exports.default = exports.getSecurityConfig;
//# sourceMappingURL=security.js.map