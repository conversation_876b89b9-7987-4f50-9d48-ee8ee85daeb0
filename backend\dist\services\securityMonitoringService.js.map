{"version": 3, "file": "securityMonitoringService.js", "sourceRoot": "", "sources": ["../../src/services/securityMonitoringService.ts"], "names": [], "mappings": ";;;;;;AAAA,sDAA8B;AAC9B,0FAAwD;AAIxD,uBAAuB;AACvB,IAAY,iBAiBX;AAjBD,WAAY,iBAAiB;IAC3B,oDAA+B,CAAA;IAC/B,oDAA+B,CAAA;IAC/B,gEAA2C,CAAA;IAC3C,gEAA2C,CAAA;IAC3C,gEAA2C,CAAA;IAC3C,gEAA2C,CAAA;IAC3C,gEAA2C,CAAA;IAC3C,sDAAiC,CAAA;IACjC,kEAA6C,CAAA;IAC7C,wDAAmC,CAAA;IACnC,sDAAiC,CAAA;IACjC,8DAAyC,CAAA;IACzC,gDAA2B,CAAA;IAC3B,oEAA+C,CAAA;IAC/C,oEAA+C,CAAA;IAC/C,gDAA2B,CAAA;AAC7B,CAAC,EAjBW,iBAAiB,iCAAjB,iBAAiB,QAiB5B;AAED,IAAY,gBAKX;AALD,WAAY,gBAAgB;IAC1B,+BAAW,CAAA;IACX,qCAAiB,CAAA;IACjB,iCAAa,CAAA;IACb,yCAAqB,CAAA;AACvB,CAAC,EALW,gBAAgB,gCAAhB,gBAAgB,QAK3B;AAiBD,MAAM,yBAAyB;IAK7B;QAHQ,oBAAe,GAAmC,IAAI,GAAG,EAAE,CAAC;QAC5D,gBAAW,GAAwB,IAAI,GAAG,EAAE,CAAC;QAGnD,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,yBAAyB,EAAE,CAAC;IACnC,CAAC;IAEO,gBAAgB;QACtB,IAAI,CAAC,MAAM,GAAG,iBAAO,CAAC,YAAY,CAAC;YACjC,KAAK,EAAE,MAAM;YACb,MAAM,EAAE,iBAAO,CAAC,MAAM,CAAC,OAAO,CAC5B,iBAAO,CAAC,MAAM,CAAC,SAAS,EAAE,EAC1B,iBAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,EACtC,iBAAO,CAAC,MAAM,CAAC,IAAI,EAAE,CACtB;YACD,WAAW,EAAE,EAAE,OAAO,EAAE,8BAA8B,EAAE;YACxD,UAAU,EAAE;gBACV,0CAA0C;gBAC1C,IAAI,mCAAe,CAAC;oBAClB,QAAQ,EAAE,0BAA0B;oBACpC,WAAW,EAAE,YAAY;oBACzB,aAAa,EAAE,IAAI;oBACnB,OAAO,EAAE,KAAK;oBACd,QAAQ,EAAE,KAAK;iBAChB,CAAC;gBACF,6CAA6C;gBAC7C,IAAI,mCAAe,CAAC;oBAClB,QAAQ,EAAE,mCAAmC;oBAC7C,WAAW,EAAE,YAAY;oBACzB,KAAK,EAAE,OAAO;oBACd,aAAa,EAAE,IAAI;oBACnB,OAAO,EAAE,KAAK;oBACd,QAAQ,EAAE,KAAK;iBAChB,CAAC;gBACF,iCAAiC;gBACjC,IAAI,iBAAO,CAAC,UAAU,CAAC,OAAO,CAAC;oBAC7B,MAAM,EAAE,iBAAO,CAAC,MAAM,CAAC,OAAO,CAC5B,iBAAO,CAAC,MAAM,CAAC,QAAQ,EAAE,EACzB,iBAAO,CAAC,MAAM,CAAC,MAAM,EAAE,CACxB;iBACF,CAAC;aACH;SACF,CAAC,CAAC;IACL,CAAC;IAEO,yBAAyB;QAC/B,iDAAiD;QACjD,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,iBAAiB,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;QAC7D,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,iBAAiB,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAAC;QACnE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,iBAAiB,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAAC;QACnE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,iBAAiB,CAAC,mBAAmB,EAAE,EAAE,CAAC,CAAC;QACpE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,iBAAiB,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAAC;QACnE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,iBAAiB,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAAC;QACnE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,iBAAiB,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;QAC9D,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,iBAAiB,CAAC,qBAAqB,EAAE,CAAC,CAAC,CAAC;QACrE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,iBAAiB,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;IAC7D,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,KAAoB;QACnC,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAElD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,OAAO,EAAE;YACvC,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,QAAQ,EAAE,KAAK,CAAC,QAAQ;YACxB,EAAE,EAAE,KAAK,CAAC,EAAE;YACZ,SAAS,EAAE,KAAK,CAAC,SAAS;YAC1B,MAAM,EAAE,KAAK,CAAC,MAAM;YACpB,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,MAAM,EAAE,KAAK,CAAC,MAAM;YACpB,OAAO,EAAE,KAAK,CAAC,OAAO;YACtB,SAAS,EAAE,KAAK,CAAC,SAAS;YAC1B,QAAQ,EAAE,KAAK,CAAC,QAAQ;SACzB,CAAC,CAAC;QAEH,mDAAmD;QACnD,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,YAAY,CACV,IAAuE,EACvE,GAAY,EACZ,IAAY,EACZ,cAAoC;QAEpC,MAAM,KAAK,GAAkB;YAC3B,IAAI;YACJ,QAAQ,EAAE,IAAI,KAAK,iBAAiB,CAAC,aAAa,CAAC,CAAC,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,gBAAgB,CAAC,GAAG;YACnG,OAAO,EAAE,IAAI,KAAK,iBAAiB,CAAC,aAAa,CAAC,CAAC,CAAC,uBAAuB,CAAC,CAAC,CAAC,mBAAmB;YACjG,EAAE,EAAE,GAAG,CAAC,EAAE,IAAI,SAAS;YACvB,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;YAChC,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,QAAQ,EAAE;YAC7B,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,EAAE,KAAK;YACpC,IAAI,EAAE,GAAG,CAAC,IAAI;YACd,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,QAAQ,EAAE,cAAc;SACzB,CAAC;QAEF,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,qBAAqB,CACnB,GAAY,EACZ,MAAc,EACd,WAA6B,gBAAgB,CAAC,IAAI,EAClD,cAAoC;QAEpC,MAAM,KAAK,GAAkB;YAC3B,IAAI,EAAE,iBAAiB,CAAC,mBAAmB;YAC3C,QAAQ;YACR,OAAO,EAAE,iCAAiC,MAAM,EAAE;YAClD,EAAE,EAAE,GAAG,CAAC,EAAE,IAAI,SAAS;YACvB,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;YAChC,MAAM,EAAG,GAAW,CAAC,IAAI,EAAE,EAAE;YAC7B,IAAI,EAAE,GAAG,CAAC,IAAI;YACd,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,OAAO,EAAE,GAAG,CAAC,IAAI;YACjB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,QAAQ,EAAE,cAAc;SACzB,CAAC;QAEF,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,GAAY,EAAE,SAAiB;QAC/C,MAAM,KAAK,GAAkB;YAC3B,IAAI,EAAE,iBAAiB,CAAC,mBAAmB;YAC3C,QAAQ,EAAE,gBAAgB,CAAC,MAAM;YACjC,OAAO,EAAE,2BAA2B,SAAS,EAAE;YAC/C,EAAE,EAAE,GAAG,CAAC,EAAE,IAAI,SAAS;YACvB,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;YAChC,MAAM,EAAG,GAAW,CAAC,IAAI,EAAE,EAAE;YAC7B,IAAI,EAAE,GAAG,CAAC,IAAI;YACd,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,QAAQ,EAAE,EAAE,SAAS,EAAE;SACxB,CAAC;QAEF,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,GAAY,EAAE,MAAc,EAAE,KAAc;QAC1D,MAAM,KAAK,GAAkB;YAC3B,IAAI,EAAE,iBAAiB,CAAC,cAAc;YACtC,QAAQ,EAAE,gBAAgB,CAAC,IAAI;YAC/B,OAAO,EAAE,4BAA4B,MAAM,EAAE;YAC7C,EAAE,EAAE,GAAG,CAAC,EAAE,IAAI,SAAS;YACvB,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;YAChC,IAAI,EAAE,GAAG,CAAC,IAAI;YACd,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,QAAQ,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE;SAC5B,CAAC;QAEF,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,mBAAmB,CACjB,GAAY,EACZ,IAA6E,EAC7E,eAAuB;QAEvB,MAAM,KAAK,GAAkB;YAC3B,IAAI;YACJ,QAAQ,EAAE,gBAAgB,CAAC,QAAQ;YACnC,OAAO,EAAE,GAAG,IAAI,WAAW;YAC3B,EAAE,EAAE,GAAG,CAAC,EAAE,IAAI,SAAS;YACvB,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;YAChC,MAAM,EAAG,GAAW,CAAC,IAAI,EAAE,EAAE;YAC7B,IAAI,EAAE,GAAG,CAAC,IAAI;YACd,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,OAAO,EAAE,GAAG,CAAC,IAAI;YACjB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,QAAQ,EAAE,EAAE,eAAe,EAAE;SAC9B,CAAC;QAEF,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,YAAqC,KAAK;QACzD,mEAAmE;QACnE,0CAA0C;QAC1C,OAAO,OAAO,CAAC,OAAO,CAAC;YACrB,SAAS;YACT,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI;YAClC,YAAY,EAAE,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC;YAClD,cAAc,EAAE,CAAC;YACjB,kBAAkB,EAAE,CAAC;YACrB,MAAM,EAAE,EAAE;YACV,aAAa,EAAE,EAAE;YACjB,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC,CAAC;IACL,CAAC;IAEO,WAAW,CAAC,QAA0B;QAC5C,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,gBAAgB,CAAC,GAAG;gBACvB,OAAO,MAAM,CAAC;YAChB,KAAK,gBAAgB,CAAC,MAAM;gBAC1B,OAAO,MAAM,CAAC;YAChB,KAAK,gBAAgB,CAAC,IAAI,CAAC;YAC3B,KAAK,gBAAgB,CAAC,QAAQ;gBAC5B,OAAO,OAAO,CAAC;YACjB;gBACE,OAAO,MAAM,CAAC;QAClB,CAAC;IACH,CAAC;IAEO,cAAc,CAAC,KAAoB;QACzC,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACvD,IAAI,CAAC,SAAS;YAAE,OAAO;QAEvB,MAAM,GAAG,GAAG,GAAG,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,EAAE,IAAI,IAAI,IAAI,EAAE,CAAC,QAAQ,EAAE,EAAE,CAAC;QACjE,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACpD,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,EAAE,YAAY,GAAG,CAAC,CAAC,CAAC;QAE5C,IAAI,YAAY,GAAG,CAAC,IAAI,SAAS,EAAE,CAAC;YAClC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,YAAY,GAAG,CAAC,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC;IAEO,YAAY,CAAC,KAAoB,EAAE,KAAa;QACtD,MAAM,YAAY,GAAG,mBAAmB,KAAK,CAAC,IAAI,wBAAwB,KAAK,oBAAoB,KAAK,CAAC,EAAE,EAAE,CAAC;QAE9G,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,EAAE;YAC9B,SAAS,EAAE,oBAAoB;YAC/B,aAAa,EAAE,KAAK;YACpB,KAAK;YACL,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC,CAAC;QAEH,0CAA0C;QAC1C,4CAA4C;QAC5C,gDAAgD;QAChD,wCAAwC;QACxC,0CAA0C;IAC5C,CAAC;CACF;AAED,qBAAqB;AACR,QAAA,kBAAkB,GAAG,IAAI,yBAAyB,EAAE,CAAC;AAElE,kBAAe,yBAAyB,CAAC"}