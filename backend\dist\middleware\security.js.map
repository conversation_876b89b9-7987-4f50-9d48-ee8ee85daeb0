{"version": 3, "file": "security.js", "sourceRoot": "", "sources": ["../../src/middleware/security.ts"], "names": [], "mappings": ";;;;;;AAAA,4EAA2C;AAC3C,0EAAyC;AACzC,iEAA4E;AAE5E,sDAA8B;AAE9B,kBAAkB;AAClB,MAAM,cAAc,GAAG,iBAAO,CAAC,YAAY,CAAC;IAC1C,KAAK,EAAE,MAAM;IACb,MAAM,EAAE,iBAAO,CAAC,MAAM,CAAC,OAAO,CAC5B,iBAAO,CAAC,MAAM,CAAC,SAAS,EAAE,EAC1B,iBAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,EACtC,iBAAO,CAAC,MAAM,CAAC,IAAI,EAAE,CACtB;IACD,WAAW,EAAE,EAAE,OAAO,EAAE,mBAAmB,EAAE;IAC7C,UAAU,EAAE;QACV,IAAI,iBAAO,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,yBAAyB,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC;QACpF,IAAI,iBAAO,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,4BAA4B,EAAE,CAAC;QACvE,IAAI,iBAAO,CAAC,UAAU,CAAC,OAAO,CAAC;YAC7B,MAAM,EAAE,iBAAO,CAAC,MAAM,CAAC,MAAM,EAAE;SAChC,CAAC;KACH;CACF,CAAC,CAAC;AAgOD,wCAAc;AA9NhB,8BAA8B;AAC9B,MAAM,YAAY,GAAG;IACnB,4BAA4B;IAC5B,OAAO,EAAE,IAAA,4BAAS,EAAC;QACjB,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,aAAa;QACvC,GAAG,EAAE,GAAG,EAAE,6CAA6C;QACvD,OAAO,EAAE;YACP,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,yDAAyD;YAClE,KAAK,EAAE,qBAAqB;SAC7B;QACD,eAAe,EAAE,IAAI;QACrB,aAAa,EAAE,KAAK;QACpB,OAAO,EAAE,CAAC,GAAY,EAAE,GAAa,EAAE,EAAE;YACvC,cAAc,CAAC,IAAI,CAAC,qBAAqB,EAAE;gBACzC,EAAE,EAAE,GAAG,CAAC,EAAE;gBACV,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;gBAChC,IAAI,EAAE,GAAG,CAAC,IAAI;gBACd,MAAM,EAAE,GAAG,CAAC,MAAM;aACnB,CAAC,CAAC;YACH,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,yDAAyD;gBAClE,KAAK,EAAE,qBAAqB;aAC7B,CAAC,CAAC;QACL,CAAC;KACF,CAAC;IAEF,oDAAoD;IACpD,IAAI,EAAE,IAAA,4BAAS,EAAC;QACd,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,aAAa;QACvC,GAAG,EAAE,CAAC,EAAE,mDAAmD;QAC3D,OAAO,EAAE;YACP,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,2DAA2D;YACpE,KAAK,EAAE,0BAA0B;SAClC;QACD,sBAAsB,EAAE,IAAI;QAC5B,OAAO,EAAE,CAAC,GAAY,EAAE,GAAa,EAAE,EAAE;YACvC,cAAc,CAAC,KAAK,CAAC,oCAAoC,EAAE;gBACzD,EAAE,EAAE,GAAG,CAAC,EAAE;gBACV,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;gBAChC,IAAI,EAAE,GAAG,CAAC,IAAI;gBACd,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,KAAK;aACtB,CAAC,CAAC;YACH,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,2DAA2D;gBACpE,KAAK,EAAE,0BAA0B;aAClC,CAAC,CAAC;QACL,CAAC;KACF,CAAC;IAEF,oBAAoB;IACpB,GAAG,EAAE,IAAA,4BAAS,EAAC;QACb,QAAQ,EAAE,EAAE,GAAG,IAAI,EAAE,WAAW;QAChC,GAAG,EAAE,CAAC,EAAE,6CAA6C;QACrD,OAAO,EAAE;YACP,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,6DAA6D;YACtE,KAAK,EAAE,yBAAyB;SACjC;QACD,OAAO,EAAE,CAAC,GAAY,EAAE,GAAa,EAAE,EAAE;YACvC,cAAc,CAAC,IAAI,CAAC,yBAAyB,EAAE;gBAC7C,EAAE,EAAE,GAAG,CAAC,EAAE;gBACV,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;gBAChC,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,KAAK;aACtB,CAAC,CAAC;YACH,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,6DAA6D;gBACtE,KAAK,EAAE,yBAAyB;aACjC,CAAC,CAAC;QACL,CAAC;KACF,CAAC;IAEF,8BAA8B;IAC9B,GAAG,EAAE,IAAA,4BAAS,EAAC;QACb,QAAQ,EAAE,EAAE,GAAG,IAAI,EAAE,WAAW;QAChC,GAAG,EAAE,EAAE,EAAE,8CAA8C;QACvD,OAAO,EAAE;YACP,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,4CAA4C;YACrD,KAAK,EAAE,yBAAyB;SACjC;KACF,CAAC;CACH,CAAC;AAoIA,oCAAY;AAlId,8CAA8C;AAC9C,MAAM,YAAY,GAAG,IAAA,2BAAQ,EAAC;IAC5B,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,aAAa;IACvC,UAAU,EAAE,EAAE,EAAE,iDAAiD;IACjE,OAAO,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,qDAAqD;IACzE,UAAU,EAAE,KAAK,EAAE,8BAA8B;IACjD,QAAQ,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,sBAAsB;CACpD,CAAC,CAAC;AA4HD,oCAAY;AA1Hd,oDAAoD;AACpD,MAAM,mBAAmB,GAAG,IAAI,yCAAiB,CAAC;IAChD,MAAM,EAAE,GAAG,EAAE,qBAAqB;IAClC,QAAQ,EAAE,GAAG,EAAE,iBAAiB;IAChC,aAAa,EAAE,GAAG,EAAE,yCAAyC;CAC9D,CAAC,CAAC;AAsHD,kDAAmB;AApHrB,4CAA4C;AAC5C,MAAM,oBAAoB,GAAG,IAAI,yCAAiB,CAAC;IACjD,MAAM,EAAE,CAAC,EAAE,qBAAqB;IAChC,QAAQ,EAAE,GAAG,EAAE,iBAAiB;IAChC,aAAa,EAAE,IAAI,EAAE,uBAAuB;CAC7C,CAAC,CAAC;AAgHD,oDAAoB;AA9GtB,8BAA8B;AACvB,MAAM,eAAe,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IACjF,4CAA4C;IAC5C,GAAG,CAAC,SAAS,CAAC,wBAAwB,EAAE,SAAS,CAAC,CAAC;IACnD,GAAG,CAAC,SAAS,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC;IACzC,GAAG,CAAC,SAAS,CAAC,kBAAkB,EAAE,eAAe,CAAC,CAAC;IACnD,GAAG,CAAC,SAAS,CAAC,iBAAiB,EAAE,iCAAiC,CAAC,CAAC;IACpE,GAAG,CAAC,SAAS,CAAC,oBAAoB,EAAE,0CAA0C,CAAC,CAAC;IAEhF,4BAA4B;IAC5B,GAAG,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;IACjC,GAAG,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;IAE3B,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAbW,QAAA,eAAe,mBAa1B;AAEF,6BAA6B;AACtB,MAAM,aAAa,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IAC/E,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAE7B,GAAG,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;QACpB,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;QACxC,MAAM,OAAO,GAAG;YACd,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,GAAG,EAAE,GAAG,CAAC,GAAG;YACZ,EAAE,EAAE,GAAG,CAAC,EAAE;YACV,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;YAChC,UAAU,EAAE,GAAG,CAAC,UAAU;YAC1B,QAAQ;YACR,MAAM,EAAG,GAAW,CAAC,IAAI,EAAE,EAAE;YAC7B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC;QAEF,IAAI,GAAG,CAAC,UAAU,IAAI,GAAG,EAAE,CAAC;YAC1B,cAAc,CAAC,KAAK,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;QAC9C,CAAC;aAAM,CAAC;YACN,cAAc,CAAC,IAAI,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAxBW,QAAA,aAAa,iBAwBxB;AAEF,gCAAgC;AACzB,MAAM,2BAA2B,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IAC7F,MAAM,kBAAkB,GAAG;QACzB,kCAAkC,EAAE,iBAAiB;QACrD,wDAAwD,EAAE,gBAAgB;QAC1E,iDAAiD,EAAE,MAAM;QACzD,sCAAsC,EAAE,iBAAiB;KAC1D,CAAC;IAEF,MAAM,SAAS,GAAG,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;IAC9C,MAAM,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC;IACpB,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAEtC,MAAM,YAAY,GAAG,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CACrD,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CACnE,CAAC;IAEF,IAAI,YAAY,EAAE,CAAC;QACjB,cAAc,CAAC,KAAK,CAAC,8BAA8B,EAAE;YACnD,EAAE,EAAE,GAAG,CAAC,EAAE;YACV,SAAS;YACT,GAAG;YACH,IAAI,EAAE,GAAG,CAAC,IAAI;YACd,OAAO,EAAE,GAAG,CAAC,OAAO;YACpB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;QAEH,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,0BAA0B;YACnC,KAAK,EAAE,qBAAqB;SAC7B,CAAC,CAAC;IACL,CAAC;IAED,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAlCW,QAAA,2BAA2B,+BAkCtC;AAEF,6CAA6C;AACtC,MAAM,4BAA4B,GAAG,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IACpG,IAAI,CAAC;QACH,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,IAAI,CAAC,KAAK,IAAI,SAAS,EAAE,CAAC;QACvD,MAAM,oBAAoB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACxC,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,MAAW,EAAE,CAAC;QACrB,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,YAAY,IAAI,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC;QAExE,cAAc,CAAC,KAAK,CAAC,8BAA8B,EAAE;YACnD,EAAE,EAAE,GAAG,CAAC,EAAE;YACV,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,KAAK;YACrB,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;YAChC,aAAa;YACb,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;QAEH,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,0CAA0C,aAAa,WAAW;YAC3E,KAAK,EAAE,wBAAwB;YAC/B,UAAU,EAAE,aAAa;SAC1B,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC;AAvBW,QAAA,4BAA4B,gCAuBvC"}