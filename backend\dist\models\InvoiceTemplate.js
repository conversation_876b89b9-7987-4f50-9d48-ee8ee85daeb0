"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importStar(require("mongoose"));
const InvoiceTemplateSchema = new mongoose_1.Schema({
    userId: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    templateName: {
        type: String,
        required: true,
        trim: true,
        maxlength: 100
    },
    description: {
        type: String,
        trim: true,
        maxlength: 500
    },
    invoiceType: {
        type: String,
        enum: ['gst', 'non-gst'],
        required: true,
        default: 'gst'
    },
    customerTemplate: {
        name: { type: String, trim: true },
        email: { type: String, trim: true, lowercase: true },
        phone: { type: String, trim: true },
        address: { type: String, trim: true },
        gstNumber: { type: String, trim: true, uppercase: true },
        stateCode: { type: String, trim: true }
    },
    itemsTemplate: [{
            description: { type: String, required: true, trim: true },
            quantity: { type: Number, min: 0.01 },
            rate: { type: Number, min: 0 },
            unit: { type: String, trim: true, default: 'pcs' },
            hsnCode: { type: String, trim: true },
            gstRate: { type: Number, min: 0, max: 28 }
        }],
    defaultSettings: {
        notes: { type: String, trim: true },
        terms: { type: String, trim: true },
        dueInDays: { type: Number, min: 0, default: 30 }
    },
    usageCount: {
        type: Number,
        default: 0,
        min: 0
    },
    lastUsedAt: {
        type: Date
    },
    category: {
        type: String,
        trim: true,
        maxlength: 50
    },
    tags: [{
            type: String,
            trim: true,
            maxlength: 30
        }],
    isActive: {
        type: Boolean,
        default: true
    },
    isDefault: {
        type: Boolean,
        default: false
    }
}, {
    timestamps: true
});
// Indexes for better query performance
InvoiceTemplateSchema.index({ userId: 1, isActive: 1 });
InvoiceTemplateSchema.index({ userId: 1, templateName: 1 });
InvoiceTemplateSchema.index({ userId: 1, category: 1 });
InvoiceTemplateSchema.index({ userId: 1, isDefault: 1 });
InvoiceTemplateSchema.index({ usageCount: -1 }); // For popular templates
// Ensure only one default template per user
InvoiceTemplateSchema.pre('save', async function (next) {
    if (this.isDefault && this.isModified('isDefault')) {
        // Remove default flag from other templates of the same user
        await mongoose_1.default.model('InvoiceTemplate').updateMany({ userId: this.userId, _id: { $ne: this._id } }, { $set: { isDefault: false } });
    }
    next();
});
// Instance methods
InvoiceTemplateSchema.methods.incrementUsage = function () {
    this.usageCount += 1;
    this.lastUsedAt = new Date();
    return this.save();
};
InvoiceTemplateSchema.methods.toInvoiceData = function (overrides = {}) {
    return {
        invoiceType: this.invoiceType,
        customer: { ...this.customerTemplate, ...overrides.customer },
        items: overrides.items || this.itemsTemplate.map((item) => ({
            description: item.description,
            quantity: item.quantity || 1,
            rate: item.rate || 0,
            unit: item.unit || 'pcs',
            hsnCode: item.hsnCode,
            gstRate: item.gstRate
        })),
        notes: overrides.notes || this.defaultSettings.notes,
        terms: overrides.terms || this.defaultSettings.terms,
        dueDate: overrides.dueDate || (this.defaultSettings.dueInDays ?
            new Date(Date.now() + this.defaultSettings.dueInDays * 24 * 60 * 60 * 1000) :
            undefined)
    };
};
const InvoiceTemplate = mongoose_1.default.model('InvoiceTemplate', InvoiceTemplateSchema);
exports.default = InvoiceTemplate;
//# sourceMappingURL=InvoiceTemplate.js.map