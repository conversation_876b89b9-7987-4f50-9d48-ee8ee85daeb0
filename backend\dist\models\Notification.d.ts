import mongoose, { Document } from 'mongoose';
export interface INotificationPreference extends Document {
    userId: mongoose.Types.ObjectId;
    emailNotifications: boolean;
    emailAddress?: string;
    complianceReminders: boolean;
    invoiceReminders: boolean;
    systemUpdates: boolean;
    marketingEmails: boolean;
    reminderTiming: {
        days: number[];
        timeOfDay: string;
        timezone: string;
    };
    maxDailyEmails: number;
    digestMode: boolean;
    createdAt: Date;
    updatedAt: Date;
}
export interface INotification extends Document {
    userId: mongoose.Types.ObjectId;
    title: string;
    message: string;
    type: 'compliance' | 'invoice' | 'system' | 'reminder' | 'alert';
    priority: 'low' | 'medium' | 'high' | 'urgent';
    relatedEntity?: {
        type: 'compliance' | 'invoice' | 'user';
        id: mongoose.Types.ObjectId;
    };
    channels: {
        email: boolean;
        inApp: boolean;
        push?: boolean;
    };
    status: 'pending' | 'sent' | 'delivered' | 'failed' | 'read';
    sentAt?: Date;
    deliveredAt?: Date;
    readAt?: Date;
    emailDetails?: {
        subject: string;
        htmlContent?: string;
        textContent?: string;
        attachments?: string[];
    };
    retryCount: number;
    maxRetries: number;
    nextRetryAt?: Date;
    scheduledFor?: Date;
    errorMessage?: string;
    createdAt: Date;
    updatedAt: Date;
}
export declare const NotificationPreference: mongoose.Model<INotificationPreference, {}, {}, {}, mongoose.Document<unknown, {}, INotificationPreference, {}> & INotificationPreference & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
export declare const Notification: mongoose.Model<INotification, {}, {}, {}, mongoose.Document<unknown, {}, INotification, {}> & INotification & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
//# sourceMappingURL=Notification.d.ts.map