import mongoose, { Document } from 'mongoose';
export interface IDocument extends Document {
    userId: mongoose.Types.ObjectId;
    fileName: string;
    originalName: string;
    mimeType: string;
    size: number;
    path: string;
    url?: string;
    category: 'invoice' | 'receipt' | 'tax_document' | 'compliance' | 'other';
    tags: string[];
    description?: string;
    parsedData?: {
        text?: string;
        entities?: Array<{
            type: string;
            value: string;
            confidence: number;
        }>;
        invoiceData?: {
            invoiceNumber?: string;
            date?: Date;
            amount?: number;
            vendor?: string;
            gstNumber?: string;
        };
    };
    hash: string;
    isEncrypted: boolean;
    blockchainHash?: string;
    transactionId?: string;
    isVerified: boolean;
    isPublic: boolean;
    sharedWith: mongoose.Types.ObjectId[];
    uploadedAt: Date;
    lastAccessed?: Date;
    downloadCount: number;
    createdAt: Date;
    updatedAt: Date;
}
declare const _default: mongoose.Model<IDocument, {}, {}, {}, mongoose.Document<unknown, {}, IDocument, {}> & IDocument & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
export default _default;
//# sourceMappingURL=Document.d.ts.map