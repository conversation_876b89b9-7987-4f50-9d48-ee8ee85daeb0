{"version": 3, "file": "securityAuditService.js", "sourceRoot": "", "sources": ["../../src/services/securityAuditService.ts"], "names": [], "mappings": ";;;;;;AAAA,2DAA6B;AAC7B,gDAAwB;AAExB,2EAAsG;AA8CtG,MAAM,oBAAoB;IAA1B;QACU,oBAAe,GAA4B,EAAE,CAAC;QAC9C,oBAAe,GAA6B,EAAE,CAAC;IAoYzD,CAAC;IAlYC;;OAEG;IACH,KAAK,CAAC,oBAAoB;QACxB,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;QAE3D,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;QAC1B,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;QAE1B,MAAM,UAAU,GAAG;YACjB,cAAc,EAAE,MAAM,IAAI,CAAC,mBAAmB,EAAE;YAChD,aAAa,EAAE,MAAM,IAAI,CAAC,kBAAkB,EAAE;YAC9C,cAAc,EAAE,MAAM,IAAI,CAAC,mBAAmB,EAAE;YAChD,eAAe,EAAE,MAAM,IAAI,CAAC,oBAAoB,EAAE;YAClD,eAAe,EAAE,MAAM,IAAI,CAAC,oBAAoB,EAAE;YAClD,OAAO,EAAE,MAAM,IAAI,CAAC,YAAY,EAAE;YAClC,aAAa,EAAE,MAAM,IAAI,CAAC,kBAAkB,EAAE;SAC/C,CAAC;QAEF,MAAM,YAAY,GAAG,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;QAE5D,MAAM,MAAM,GAAwB;YAClC,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,YAAY;YACZ,UAAU;YACV,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,eAAe,EAAE,IAAI,CAAC,eAAe;SACtC,CAAC;QAEF,uBAAuB;QACvB,8CAAkB,CAAC,gBAAgB,CAAC;YAClC,IAAI,EAAE,6CAAiB,CAAC,mBAAmB;YAC3C,QAAQ,EAAE,4CAAgB,CAAC,GAAG;YAC9B,OAAO,EAAE,wCAAwC,YAAY,MAAM;YACnE,EAAE,EAAE,QAAQ;YACZ,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,QAAQ,EAAE,EAAE,WAAW,EAAE,MAAM,EAAE;SAClC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB;QAC/B,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,MAAM,QAAQ,GAAG,GAAG,CAAC;QAErB,0BAA0B;QAC1B,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC;QACzC,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YACxC,MAAM,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;YAClD,IAAI,CAAC,gBAAgB,CAAC;gBACpB,EAAE,EAAE,UAAU;gBACd,QAAQ,EAAE,MAAM;gBAChB,QAAQ,EAAE,gBAAgB;gBAC1B,KAAK,EAAE,iBAAiB;gBACxB,WAAW,EAAE,gDAAgD;gBAC7D,MAAM,EAAE,gDAAgD;gBACxD,WAAW,EAAE,mEAAmE;gBAChF,GAAG,EAAE,SAAS;aACf,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;YACjD,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QAED,yBAAyB;QACzB,MAAM,YAAY,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,IAAI,CAAC,CAAC;QACjE,IAAI,YAAY,GAAG,EAAE,EAAE,CAAC;YACtB,MAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;YACrC,IAAI,CAAC,gBAAgB,CAAC;gBACpB,EAAE,EAAE,UAAU;gBACd,QAAQ,EAAE,QAAQ;gBAClB,QAAQ,EAAE,gBAAgB;gBAC1B,KAAK,EAAE,uBAAuB;gBAC9B,WAAW,EAAE,+BAA+B;gBAC5C,MAAM,EAAE,wDAAwD;gBAChE,WAAW,EAAE,uCAAuC;gBACpD,GAAG,EAAE,SAAS;aACf,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;YACrD,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QAED,+BAA+B;QAC/B,IAAI,MAAM,IAAI,CAAC,eAAe,CAAC,kCAAkC,CAAC,EAAE,CAAC;YACnE,MAAM,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;YAC1D,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;YAC3D,IAAI,CAAC,iBAAiB,CAAC;gBACrB,QAAQ,EAAE,MAAM;gBAChB,QAAQ,EAAE,gBAAgB;gBAC1B,KAAK,EAAE,uCAAuC;gBAC9C,WAAW,EAAE,uCAAuC;gBACpD,cAAc,EAAE,iCAAiC;gBACjD,eAAe,EAAE,UAAU;aAC5B,CAAC,CAAC;QACL,CAAC;QAED,2BAA2B;QAC3B,IAAI,MAAM,IAAI,CAAC,uBAAuB,EAAE,EAAE,CAAC;YACzC,MAAM,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;YACtD,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;YAClD,IAAI,CAAC,iBAAiB,CAAC;gBACrB,QAAQ,EAAE,QAAQ;gBAClB,QAAQ,EAAE,gBAAgB;gBAC1B,KAAK,EAAE,0BAA0B;gBACjC,WAAW,EAAE,qCAAqC;gBAClD,cAAc,EAAE,+DAA+D;gBAC/E,eAAe,EAAE,UAAU;aAC5B,CAAC,CAAC;QACL,CAAC;QAED,mCAAmC;QACnC,IAAI,MAAM,IAAI,CAAC,eAAe,CAAC,4BAA4B,CAAC,EAAE,CAAC;YAC7D,MAAM,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;YACrD,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;YACtD,IAAI,CAAC,gBAAgB,CAAC;gBACpB,EAAE,EAAE,UAAU;gBACd,QAAQ,EAAE,QAAQ;gBAClB,QAAQ,EAAE,gBAAgB;gBAC1B,KAAK,EAAE,gCAAgC;gBACvC,WAAW,EAAE,8CAA8C;gBAC3D,MAAM,EAAE,gDAAgD;gBACxD,WAAW,EAAE,6CAA6C;gBAC1D,GAAG,EAAE,SAAS;aACf,CAAC,CAAC;QACL,CAAC;QAED,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;IAC7C,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB;QAC9B,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,MAAM,QAAQ,GAAG,GAAG,CAAC;QAErB,gCAAgC;QAChC,IAAI,MAAM,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;YAC9B,MAAM,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;YACxD,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;YAC/D,IAAI,CAAC,iBAAiB,CAAC;gBACrB,QAAQ,EAAE,MAAM;gBAChB,QAAQ,EAAE,eAAe;gBACzB,KAAK,EAAE,8BAA8B;gBACrC,WAAW,EAAE,4CAA4C;gBACzD,cAAc,EAAE,gDAAgD;gBAChE,eAAe,EAAE,UAAU;aAC5B,CAAC,CAAC;QACL,CAAC;QAED,8BAA8B;QAC9B,IAAI,MAAM,IAAI,CAAC,eAAe,CAAC,2BAA2B,CAAC,EAAE,CAAC;YAC5D,MAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;YAChD,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;YACzC,IAAI,CAAC,gBAAgB,CAAC;gBACpB,EAAE,EAAE,WAAW;gBACf,QAAQ,EAAE,MAAM;gBAChB,QAAQ,EAAE,eAAe;gBACzB,KAAK,EAAE,2BAA2B;gBAClC,WAAW,EAAE,gDAAgD;gBAC7D,MAAM,EAAE,sDAAsD;gBAC9D,WAAW,EAAE,gDAAgD;gBAC7D,GAAG,EAAE,SAAS;aACf,CAAC,CAAC;QACL,CAAC;QAED,4CAA4C;QAC5C,IAAI,MAAM,IAAI,CAAC,qCAAqC,EAAE,EAAE,CAAC;YACvD,MAAM,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;YAC3D,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;YACjE,IAAI,CAAC,iBAAiB,CAAC;gBACrB,QAAQ,EAAE,QAAQ;gBAClB,QAAQ,EAAE,eAAe;gBACzB,KAAK,EAAE,yCAAyC;gBAChD,WAAW,EAAE,4CAA4C;gBACzD,cAAc,EAAE,mDAAmD;gBACnE,eAAe,EAAE,UAAU;aAC5B,CAAC,CAAC;QACL,CAAC;QAED,yCAAyC;QACzC,IAAI,MAAM,IAAI,CAAC,yBAAyB,EAAE,EAAE,CAAC;YAC3C,MAAM,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;YAC3D,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;YAC9D,IAAI,CAAC,iBAAiB,CAAC;gBACrB,QAAQ,EAAE,QAAQ;gBAClB,QAAQ,EAAE,eAAe;gBACzB,KAAK,EAAE,wCAAwC;gBAC/C,WAAW,EAAE,kDAAkD;gBAC/D,cAAc,EAAE,qCAAqC;gBACrD,eAAe,EAAE,UAAU;aAC5B,CAAC,CAAC;QACL,CAAC;QAED,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;IAC7C,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB;QAC/B,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,MAAM,QAAQ,GAAG,GAAG,CAAC;QAErB,+BAA+B;QAC/B,IAAI,MAAM,IAAI,CAAC,wBAAwB,EAAE,EAAE,CAAC;YAC1C,MAAM,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;YACtD,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;YACvD,IAAI,CAAC,gBAAgB,CAAC;gBACpB,EAAE,EAAE,UAAU;gBACd,QAAQ,EAAE,MAAM;gBAChB,QAAQ,EAAE,iBAAiB;gBAC3B,KAAK,EAAE,4BAA4B;gBACnC,WAAW,EAAE,iDAAiD;gBAC9D,MAAM,EAAE,8CAA8C;gBACtD,WAAW,EAAE,qDAAqD;gBAClE,GAAG,EAAE,SAAS;aACf,CAAC,CAAC;QACL,CAAC;QAED,kCAAkC;QAClC,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;YACxE,MAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;YAChD,IAAI,CAAC,gBAAgB,CAAC;gBACpB,EAAE,EAAE,UAAU;gBACd,QAAQ,EAAE,MAAM;gBAChB,QAAQ,EAAE,iBAAiB;gBAC3B,KAAK,EAAE,2BAA2B;gBAClC,WAAW,EAAE,qCAAqC;gBAClD,MAAM,EAAE,oCAAoC;gBAC5C,WAAW,EAAE,yCAAyC;gBACtD,GAAG,EAAE,SAAS;aACf,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;YAC5D,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QAED,8BAA8B;QAC9B,IAAI,MAAM,IAAI,CAAC,eAAe,CAAC,qCAAqC,CAAC,EAAE,CAAC;YACtE,MAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;YACjD,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;YAClD,IAAI,CAAC,gBAAgB,CAAC;gBACpB,EAAE,EAAE,UAAU;gBACd,QAAQ,EAAE,QAAQ;gBAClB,QAAQ,EAAE,iBAAiB;gBAC3B,KAAK,EAAE,4BAA4B;gBACnC,WAAW,EAAE,sCAAsC;gBACnD,MAAM,EAAE,qDAAqD;gBAC7D,WAAW,EAAE,4CAA4C;gBACzD,GAAG,EAAE,QAAQ;aACd,CAAC,CAAC;QACL,CAAC;QAED,iCAAiC;QACjC,IAAI,MAAM,IAAI,CAAC,qBAAqB,EAAE,EAAE,CAAC;YACvC,MAAM,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;YACrD,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;YACzD,IAAI,CAAC,iBAAiB,CAAC;gBACrB,QAAQ,EAAE,QAAQ;gBAClB,QAAQ,EAAE,iBAAiB;gBAC3B,KAAK,EAAE,oCAAoC;gBAC3C,WAAW,EAAE,kDAAkD;gBAC/D,cAAc,EAAE,oCAAoC;gBACpD,eAAe,EAAE,UAAU;aAC5B,CAAC,CAAC;QACL,CAAC;QAED,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;IAC7C,CAAC;IAED,iBAAiB;IACT,KAAK,CAAC,eAAe,CAAC,QAAgB;QAC5C,IAAI,CAAC;YACH,MAAM,kBAAE,CAAC,MAAM,CAAC,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,QAAQ,CAAC,CAAC,CAAC;YACpD,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,uBAAuB;QACnC,qDAAqD;QACrD,OAAO,OAAO,CAAC,GAAG,CAAC,UAAU,KAAK,SAAS,CAAC;IAC9C,CAAC;IAEO,KAAK,CAAC,YAAY;QACxB,oEAAoE;QACpE,OAAO,MAAM,IAAI,CAAC,eAAe,CAAC,wBAAwB,CAAC,CAAC;IAC9D,CAAC;IAEO,KAAK,CAAC,qCAAqC;QACjD,4CAA4C;QAC5C,OAAO,IAAI,CAAC,CAAC,kDAAkD;IACjE,CAAC;IAEO,KAAK,CAAC,yBAAyB;QACrC,yCAAyC;QACzC,OAAO,IAAI,CAAC,CAAC,kDAAkD;IACjE,CAAC;IAEO,KAAK,CAAC,wBAAwB;QACpC,6CAA6C;QAC7C,OAAO,MAAM,IAAI,CAAC,eAAe,CAAC,wBAAwB,CAAC,CAAC;IAC9D,CAAC;IAEO,KAAK,CAAC,qBAAqB;QACjC,qCAAqC;QACrC,OAAO,KAAK,CAAC,CAAC,wDAAwD;IACxE,CAAC;IAEO,qBAAqB,CAAC,UAAe;QAC3C,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,GAAQ,EAAE,EAAE,CACxD,CAAC,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,QAAQ,CAAC,GAAG,GAAG,CACjC,CAAC;QACF,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;IACvE,CAAC;IAEO,gBAAgB,CAAC,IAAyC;QAChE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;YACxB,GAAG,IAAI;YACP,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC;SACxC,CAAC,CAAC;IACL,CAAC;IAEO,iBAAiB,CAAC,GAA2B;QACnD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACjC,CAAC;IAEO,aAAa,CAAC,QAAgB;QACpC,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,UAAU,CAAC,CAAC,OAAO,GAAG,CAAC;YAC5B,KAAK,MAAM,CAAC,CAAC,OAAO,GAAG,CAAC;YACxB,KAAK,QAAQ,CAAC,CAAC,OAAO,GAAG,CAAC;YAC1B,KAAK,KAAK,CAAC,CAAC,OAAO,GAAG,CAAC;YACvB,OAAO,CAAC,CAAC,OAAO,GAAG,CAAC;QACtB,CAAC;IACH,CAAC;IAED,iDAAiD;IACzC,KAAK,CAAC,oBAAoB;QAChC,OAAO,EAAE,KAAK,EAAE,EAAE,EAAE,QAAQ,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,8BAA8B,CAAC,EAAE,CAAC;IAC5F,CAAC;IAEO,KAAK,CAAC,oBAAoB;QAChC,OAAO,EAAE,KAAK,EAAE,EAAE,EAAE,QAAQ,EAAE,GAAG,EAAE,MAAM,EAAE,CAAC,qBAAqB,CAAC,EAAE,MAAM,EAAE,CAAC,iBAAiB,CAAC,EAAE,CAAC;IACpG,CAAC;IAEO,KAAK,CAAC,YAAY;QACxB,OAAO,EAAE,KAAK,EAAE,EAAE,EAAE,QAAQ,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,8BAA8B,CAAC,EAAE,CAAC;IAC5F,CAAC;IAEO,KAAK,CAAC,kBAAkB;QAC9B,OAAO,EAAE,KAAK,EAAE,EAAE,EAAE,QAAQ,EAAE,GAAG,EAAE,MAAM,EAAE,CAAC,uBAAuB,CAAC,EAAE,MAAM,EAAE,CAAC,wBAAwB,CAAC,EAAE,CAAC;IAC7G,CAAC;CACF;AAEY,QAAA,aAAa,GAAG,IAAI,oBAAoB,EAAE,CAAC;AACxD,kBAAe,oBAAoB,CAAC"}