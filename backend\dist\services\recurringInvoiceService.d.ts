export declare class RecurringInvoiceService {
    private static instance;
    private emailService;
    constructor();
    static getInstance(): RecurringInvoiceService;
    /**
     * Initialize cron jobs for recurring invoice generation
     */
    private initializeCronJobs;
    /**
     * Process all due recurring invoices
     */
    processRecurringInvoices(): Promise<void>;
    /**
     * Generate a new invoice from a recurring invoice template
     */
    generateInvoiceFromRecurring(recurringInvoice: any): Promise<any>;
    /**
     * Generate unique invoice number
     */
    private generateInvoiceNumber;
    /**
     * Send recurring invoice email to customer
     */
    private sendRecurringInvoiceEmail;
    /**
     * Send notification to user about generated invoice
     */
    private sendUserNotification;
    /**
     * Create a new recurring invoice
     */
    createRecurringInvoice(userId: string, recurringInvoiceData: any): Promise<any>;
    /**
     * Get user's recurring invoices
     */
    getUserRecurringInvoices(userId: string): Promise<any[]>;
    /**
     * Update recurring invoice
     */
    updateRecurringInvoice(recurringInvoiceId: string, userId: string, updateData: any): Promise<any>;
    /**
     * Delete recurring invoice
     */
    deleteRecurringInvoice(recurringInvoiceId: string, userId: string): Promise<boolean>;
    /**
     * Manual trigger for testing
     */
    triggerRecurringInvoiceGeneration(): Promise<void>;
}
export default RecurringInvoiceService;
//# sourceMappingURL=recurringInvoiceService.d.ts.map