export interface CashFlowData {
    period: string;
    income: number;
    outstanding: number;
    overdue: number;
    netCashFlow: number;
}
export interface CashFlowSummary {
    totalRevenue: number;
    totalOutstanding: number;
    totalOverdue: number;
    averagePaymentTime: number;
    cashFlowTrend: 'positive' | 'negative' | 'stable';
    monthlyData: CashFlowData[];
    weeklyData: CashFlowData[];
    paymentStatusBreakdown: {
        paid: {
            count: number;
            amount: number;
        };
        pending: {
            count: number;
            amount: number;
        };
        partial: {
            count: number;
            amount: number;
        };
        overdue: {
            count: number;
            amount: number;
        };
    };
    topCustomers: Array<{
        name: string;
        totalAmount: number;
        invoiceCount: number;
        averagePaymentTime: number;
    }>;
    projectedCashFlow: Array<{
        period: string;
        expectedIncome: number;
        confidence: 'high' | 'medium' | 'low';
    }>;
}
export declare class CashFlowAnalyticsService {
    /**
     * Get comprehensive cash flow analytics for a user
     */
    getCashFlowAnalytics(userId: string, period?: 'month' | 'quarter' | 'year'): Promise<CashFlowSummary>;
    /**
     * Calculate total revenue from paid invoices
     */
    private calculateTotalRevenue;
    /**
     * Calculate total outstanding amount
     */
    private calculateOutstanding;
    /**
     * Calculate total overdue amount
     */
    private calculateOverdue;
    /**
     * Calculate average payment time in days
     */
    private calculateAveragePaymentTime;
    /**
     * Determine cash flow trend
     */
    private determineCashFlowTrend;
    /**
     * Generate monthly cash flow data
     */
    private generateMonthlyData;
    /**
     * Generate weekly cash flow data
     */
    private generateWeeklyData;
    /**
     * Get payment status breakdown
     */
    private getPaymentStatusBreakdown;
    /**
     * Get top customers by revenue
     */
    private getTopCustomers;
    /**
     * Generate cash flow projection
     */
    private generateCashFlowProjection;
}
export default CashFlowAnalyticsService;
//# sourceMappingURL=cashFlowAnalyticsService.d.ts.map