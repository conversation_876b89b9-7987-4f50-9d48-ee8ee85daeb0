"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importStar(require("mongoose"));
const crypto_1 = __importDefault(require("crypto"));
const ApiKeySchema = new mongoose_1.Schema({
    userId: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User',
        required: true,
        index: true
    },
    name: {
        type: String,
        required: true,
        trim: true,
        maxlength: 100
    },
    keyId: {
        type: String,
        required: true,
        unique: true,
        index: true
    },
    hashedKey: {
        type: String,
        required: true,
        select: false // Don't include in queries by default
    },
    permissions: {
        invoices: {
            read: { type: Boolean, default: true },
            create: { type: Boolean, default: false },
            update: { type: Boolean, default: false },
            delete: { type: Boolean, default: false }
        },
        customers: {
            read: { type: Boolean, default: true },
            create: { type: Boolean, default: false },
            update: { type: Boolean, default: false },
            delete: { type: Boolean, default: false }
        },
        documents: {
            read: { type: Boolean, default: true },
            create: { type: Boolean, default: false }
        }
    },
    isActive: {
        type: Boolean,
        default: true
    },
    lastUsed: {
        type: Date
    },
    usageCount: {
        type: Number,
        default: 0
    },
    rateLimit: {
        requestsPerMinute: { type: Number, default: 60 },
        requestsPerHour: { type: Number, default: 1000 },
        requestsPerDay: { type: Number, default: 10000 }
    },
    expiresAt: {
        type: Date
    }
}, {
    timestamps: true
});
// Indexes for better query performance
ApiKeySchema.index({ userId: 1, isActive: 1 });
ApiKeySchema.index({ keyId: 1, isActive: 1 });
ApiKeySchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });
// Generate API key
ApiKeySchema.methods.generateKey = function () {
    // Generate a unique key ID (public identifier)
    this.keyId = `ak_${crypto_1.default.randomBytes(16).toString('hex')}`;
    // Generate the actual API key (secret)
    const apiKey = `sk_${crypto_1.default.randomBytes(32).toString('hex')}`;
    // Hash the API key for storage
    this.hashedKey = crypto_1.default.createHash('sha256').update(apiKey).digest('hex');
    return { keyId: this.keyId, apiKey };
};
// Verify API key
ApiKeySchema.methods.verifyKey = function (providedKey) {
    const hashedProvidedKey = crypto_1.default.createHash('sha256').update(providedKey).digest('hex');
    return this.hashedKey === hashedProvidedKey;
};
// Static method to find and verify API key
ApiKeySchema.statics.findByKey = async function (apiKey) {
    // Extract key ID from the API key format
    if (!apiKey.startsWith('sk_')) {
        return null;
    }
    // Find all active API keys and check each one
    const apiKeys = await this.find({ isActive: true }).select('+hashedKey');
    for (const key of apiKeys) {
        if (key.verifyKey(apiKey)) {
            // Update last used timestamp and usage count
            key.lastUsed = new Date();
            key.usageCount += 1;
            await key.save();
            return key;
        }
    }
    return null;
};
// Pre-save middleware to set default expiration (1 year)
ApiKeySchema.pre('save', function (next) {
    if (this.isNew && !this.expiresAt) {
        this.expiresAt = new Date(Date.now() + 365 * 24 * 60 * 60 * 1000); // 1 year
    }
    next();
});
exports.default = mongoose_1.default.model('ApiKey', ApiKeySchema);
//# sourceMappingURL=ApiKey.js.map