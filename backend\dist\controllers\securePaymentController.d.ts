import { Request, Response } from 'express';
export declare const generatePaymentLink: (req: Request, res: Response) => Promise<void>;
export declare const validatePaymentToken: (req: Request, res: Response) => Promise<void>;
export declare const initiateSecurePayment: (req: Request, res: Response) => Promise<void>;
export declare const completeSecurePayment: (req: Request, res: Response) => Promise<void>;
export declare const getPaymentTokenDetails: (req: Request, res: Response) => Promise<void>;
export declare const getInvoicePaymentTokens: (req: Request, res: Response) => Promise<void>;
export declare const upiCallback: (req: Request, res: Response) => Promise<void>;
export declare const cleanupExpiredTokens: (req: Request, res: Response) => Promise<void>;
//# sourceMappingURL=securePaymentController.d.ts.map